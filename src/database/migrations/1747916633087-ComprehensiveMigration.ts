import { MigrationInterface, QueryRunner } from 'typeorm';

export class ComprehensiveWorkflowAndGrantCallCloseMigration1747916633087 implements MigrationInterface {
  name = 'ComprehensiveWorkflowAndGrantCallCloseMigration1747916633087';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, drop any existing foreign key constraints that might interfere
    await queryRunner.query(`ALTER TABLE "grant_application" DROP CONSTRAINT IF EXISTS "FK_eef23d9c2f14c87587b3f5f20f5"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);

    // Drop existing enum types if they exist
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."WorkflowEntityType" CASCADE`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."StageCode" CASCADE`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."StageTransitionType" CASCADE`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."DistributionType" CASCADE`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."WorkflowStatus" CASCADE`); // For workflow_state.status
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."ApplicationStatus" CASCADE`);

    // Create new enum types
    await queryRunner.query(`CREATE TYPE "public"."WorkflowEntityType" AS ENUM('PROGRAM', 'CALL', 'APPLICATION')`);
    await queryRunner.query(
      `CREATE TYPE "public"."StageCode" AS ENUM('GP_OPEN', 'GP_FINALIZED', 'GC_CLOSED', 'GC_OPEN_FOR_APPLICATIONS', 'GC_SCREENING', 'GC_COMMUNITY_VOTING', 'GC_ONBOARDING', 'GC_FINAL_COMMUNITY_VOTING', 'GC_FINALIZED', 'GA_SCREENING', 'GA_QUALIFICATION', 'GA_INTERVIEW', 'GA_DUE_DILIGENCE', 'GA_TOWN_HALL', 'GA_FINAL_QUALIFICATION')`,
    );
    await queryRunner.query(`CREATE TYPE "public"."StageTransitionType" AS ENUM('MANUAL', 'AUTOMATIC')`);
    await queryRunner.query(`CREATE TYPE "public"."DistributionType" AS ENUM('PERCENTAGE', 'FIXED_AMOUNT')`);
    await queryRunner.query( // For workflow_state.status
      `CREATE TYPE "public"."WorkflowStatus" AS ENUM('IN_PROGRESS', 'READY_FOR_NEXT_STEP', 'ACTION_REQUIRED', 'APPROVED', 'REJECTED', 'WITHDRAWN')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."ApplicationStatus" AS ENUM('OPEN', 'WITHDRAWN', 'REJECTED', 'APPROVED')`,
    );

    // Schema modifications for various tables...
    const hasEntityTypeColumn = await queryRunner.hasColumn('workflow_templates', 'entityType');
    if (!hasEntityTypeColumn) {
      await queryRunner.query(
        `ALTER TABLE "workflow_templates" ADD COLUMN "entityType" "public"."WorkflowEntityType" NOT NULL DEFAULT 'PROGRAM'`
      );
    } else {
      await queryRunner.query(
        `ALTER TABLE "workflow_templates" ALTER COLUMN "entityType" TYPE "public"."WorkflowEntityType" USING "entityType"::"text"::"public"."WorkflowEntityType"`
      );
    }

    const hasCodeColumn = await queryRunner.hasColumn('workflow_step_definitions', 'code');
    if (!hasCodeColumn) {
      await queryRunner.query(
        `ALTER TABLE "workflow_step_definitions" ADD COLUMN "code" "public"."StageCode"`
      );
      await queryRunner.query(`
        UPDATE "workflow_step_definitions"
        SET "code" = CASE
          WHEN "sequenceNumber" = 1 THEN 'GP_OPEN'::"public"."StageCode"
          WHEN "sequenceNumber" = 2 THEN 'GP_FINALIZED'::"public"."StageCode"
          WHEN "sequenceNumber" = 3 THEN 'GC_CLOSED'::"public"."StageCode"
          WHEN "sequenceNumber" = 4 THEN 'GC_OPEN_FOR_APPLICATIONS'::"public"."StageCode"
          WHEN "sequenceNumber" = 5 THEN 'GC_SCREENING'::"public"."StageCode"
          WHEN "sequenceNumber" = 6 THEN 'GC_COMMUNITY_VOTING'::"public"."StageCode"
          WHEN "sequenceNumber" = 7 THEN 'GC_ONBOARDING'::"public"."StageCode"
          WHEN "sequenceNumber" = 8 THEN 'GC_FINAL_COMMUNITY_VOTING'::"public"."StageCode"
          WHEN "sequenceNumber" = 9 THEN 'GC_FINALIZED'::"public"."StageCode"
          WHEN "sequenceNumber" = 10 THEN 'GA_SCREENING'::"public"."StageCode"
          WHEN "sequenceNumber" = 11 THEN 'GA_QUALIFICATION'::"public"."StageCode"
          WHEN "sequenceNumber" = 12 THEN 'GA_INTERVIEW'::"public"."StageCode"
          WHEN "sequenceNumber" = 13 THEN 'GA_DUE_DILIGENCE'::"public"."StageCode"
          WHEN "sequenceNumber" = 14 THEN 'GA_TOWN_HALL'::"public"."StageCode"
          WHEN "sequenceNumber" = 15 THEN 'GA_FINAL_QUALIFICATION'::"public"."StageCode"
          ELSE 'GP_OPEN'::"public"."StageCode"
        END
        WHERE "code" IS NULL
      `);
      await queryRunner.query(
        `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" SET NOT NULL`
      );
    } else {
      await queryRunner.query(
        `UPDATE "workflow_step_definitions" SET "code" = "code"::"text"::"public"."StageCode" WHERE "code" IS NOT NULL`
      );
      await queryRunner.query(
        `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" TYPE "public"."StageCode" USING "code"::"text"::"public"."StageCode"`
      );
      const codeColumnInfo = await queryRunner.getTable('workflow_step_definitions').then(table => table?.columns.find(col => col.name === 'code'));
      if (codeColumnInfo && codeColumnInfo.isNullable) {
         await queryRunner.query(`UPDATE "workflow_step_definitions" SET "code" = 'GP_OPEN'::"public"."StageCode" WHERE "code" IS NULL`);
         await queryRunner.query(`ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" SET NOT NULL`);
      }
    }

    const hasTransitionTypeColumn = await queryRunner.hasColumn('workflow_step_definitions', 'transitionType');
    if (!hasTransitionTypeColumn) {
      await queryRunner.query(
        `ALTER TABLE "workflow_step_definitions" ADD COLUMN "transitionType" "public"."StageTransitionType" NOT NULL DEFAULT 'MANUAL'`
      );
    } else {
      await queryRunner.query(
        `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "transitionType" TYPE "public"."StageTransitionType" USING "transitionType"::"text"::"public"."StageTransitionType"`
      );
    }

    // ================= FIX FOR workflow_state.status =================
    const workflowStateTable = await queryRunner.getTable('workflow_state');
    const hasWFStateStatusColumn = !!workflowStateTable?.columns.find(col => col.name === 'status');
    const defaultWorkflowStatus = 'IN_PROGRESS';

    if (!hasWFStateStatusColumn) {
      console.log("Adding 'status' column to 'workflow_state'.");
      await queryRunner.query(
        `ALTER TABLE "workflow_state" ADD COLUMN "status" "public"."WorkflowStatus"`
      );
      console.log(`Updating existing rows in 'workflow_state' to set 'status' to '${defaultWorkflowStatus}'.`);
      await queryRunner.query(
        `UPDATE "workflow_state" SET "status" = '${defaultWorkflowStatus}'::"public"."WorkflowStatus" WHERE "status" IS NULL`
      );
      console.log("Altering 'status' column in 'workflow_state' to NOT NULL.");
      await queryRunner.query(
        `ALTER TABLE "workflow_state" ALTER COLUMN "status" SET NOT NULL`
      );
    } else {
      console.log("Altering existing 'status' column in 'workflow_state'.");
      await queryRunner.query(
        `UPDATE "workflow_state" SET "status" = '${defaultWorkflowStatus}'::"public"."WorkflowStatus" WHERE "status" IS NULL`
      );
      await queryRunner.query(
        `ALTER TABLE "workflow_state" ALTER COLUMN "status" TYPE "public"."WorkflowStatus" USING "status"::"text"::"public"."WorkflowStatus"`
      );
      const statusColumnInfo = workflowStateTable?.columns.find(col => col.name === 'status');
      if (statusColumnInfo && statusColumnInfo.isNullable) {
        console.log("Ensuring 'status' column in 'workflow_state' is NOT NULL.");
        await queryRunner.query(
          `ALTER TABLE "workflow_state" ALTER COLUMN "status" SET NOT NULL`
        );
      }
    }
    console.log(`Setting default for 'status' column in 'workflow_state' to '${defaultWorkflowStatus}'.`);
    await queryRunner.query(
      `ALTER TABLE "workflow_state" ALTER COLUMN "status" SET DEFAULT '${defaultWorkflowStatus}'::"public"."WorkflowStatus"`
    );
    // ================= END OF FIX for workflow_state.status ==================

    // ================= FIX FOR grant_distribution_rules.type =================
    const grantDistributionRulesTable = await queryRunner.getTable('grant_distribution_rules');
    const hasGDRTypeColumn = !!grantDistributionRulesTable?.columns.find(col => col.name === 'type');
    const defaultDistributionType = 'PERCENTAGE'; 

    if (!hasGDRTypeColumn) {
      console.log("Adding 'type' column to 'grant_distribution_rules'.");
      await queryRunner.query(
        `ALTER TABLE "grant_distribution_rules" ADD COLUMN "type" "public"."DistributionType"`
      );
      console.log(`Updating existing rows in 'grant_distribution_rules' to set 'type' to '${defaultDistributionType}'.`);
      await queryRunner.query(
        `UPDATE "grant_distribution_rules" SET "type" = '${defaultDistributionType}'::"public"."DistributionType" WHERE "type" IS NULL`
      );
      console.log("Altering 'type' column in 'grant_distribution_rules' to NOT NULL.");
      await queryRunner.query(
        `ALTER TABLE "grant_distribution_rules" ALTER COLUMN "type" SET NOT NULL`
      );
    } else {
      console.log("Altering existing 'type' column in 'grant_distribution_rules'.");
      await queryRunner.query(
        `UPDATE "grant_distribution_rules" SET "type" = '${defaultDistributionType}'::"public"."DistributionType" WHERE "type" IS NULL`
      );
      await queryRunner.query(
        `ALTER TABLE "grant_distribution_rules" ALTER COLUMN "type" TYPE "public"."DistributionType" USING "type"::"text"::"public"."DistributionType"`
      );
      const typeColumnInfo = grantDistributionRulesTable?.columns.find(col => col.name === 'type');
      if (typeColumnInfo && typeColumnInfo.isNullable) {
        console.log("Ensuring 'type' column in 'grant_distribution_rules' is NOT NULL.");
        await queryRunner.query(
          `ALTER TABLE "grant_distribution_rules" ALTER COLUMN "type" SET NOT NULL`
        );
      }
    }
    console.log(`Setting default for 'type' column in 'grant_distribution_rules' to '${defaultDistributionType}'.`);
    await queryRunner.query(
      `ALTER TABLE "grant_distribution_rules" ALTER COLUMN "type" SET DEFAULT '${defaultDistributionType}'::"public"."DistributionType"`
    );
    // ================= END OF FIX for grant_distribution_rules.type ==========

    const hasGrantApplicationStatusColumn = await queryRunner.hasColumn('grant_application', 'status');
    if (hasGrantApplicationStatusColumn) {
      await queryRunner.query(
        `ALTER TABLE "grant_application" ALTER COLUMN "status" TYPE "public"."ApplicationStatus" USING "status"::"text"::"public"."ApplicationStatus"`,
      );
      await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "status" SET DEFAULT 'OPEN'`);
    }

    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" ALTER COLUMN "transitionType" SET DEFAULT 'MANUAL'`);

    // De-duplication logic
    const duplicatesToRemapCTE = `
        WITH duplicates_to_remap AS (
            SELECT
                wsd.id AS duplicate_id, 
                k.min_id AS target_id   
            FROM "workflow_step_definitions" wsd
            JOIN (
                SELECT
                    MIN(id) AS min_id,
                    "workflowTemplateId",
                    code
                FROM "workflow_step_definitions"
                WHERE code IS NOT NULL 
                GROUP BY "workflowTemplateId", code
                HAVING COUNT(*) > 1
            ) k ON wsd."workflowTemplateId" = k."workflowTemplateId"
                 AND wsd.code = k.code
            WHERE wsd.id != k.min_id 
        )
    `;

    const fkColumnInWorkflowState = "currentStepDefinitionId"; 
    console.log(`Attempting to re-parent foreign keys in 'workflow_state' table, column '${fkColumnInWorkflowState}', before de-duplication.`);
    await queryRunner.query(`
        ${duplicatesToRemapCTE}
        UPDATE "workflow_state" ws
        SET "${fkColumnInWorkflowState}" = dtr.target_id
        FROM duplicates_to_remap dtr
        WHERE ws."${fkColumnInWorkflowState}" = dtr.duplicate_id
          AND dtr.target_id IS NOT NULL; 
    `);
    console.log("Re-parenting of foreign keys in 'workflow_state' complete.");

    const fkColumnInGrantCallStageSettings = "workflowStepDefinitionId";
    const uniqueConstraintColumnsGrantCallStageSettings = ["grantCallId", "workflowStepDefinitionId"];

    console.log(`Handling 'grant_call_stage_settings' for column '${fkColumnInGrantCallStageSettings}' considering unique constraint on (${uniqueConstraintColumnsGrantCallStageSettings.join(', ')}).`);

    console.log(`Deleting rows from 'grant_call_stage_settings' that would cause unique constraint violations upon re-parenting.`);
    await queryRunner.query(`
        ${duplicatesToRemapCTE}
        DELETE FROM "grant_call_stage_settings" gcss_to_delete
        WHERE EXISTS (
            SELECT 1
            FROM duplicates_to_remap dtr
            WHERE gcss_to_delete."${fkColumnInGrantCallStageSettings}" = dtr.duplicate_id 
              AND dtr.target_id IS NOT NULL
              AND EXISTS ( 
                  SELECT 1
                  FROM "grant_call_stage_settings" gcss_existing
                  WHERE gcss_existing."grantCallId" = gcss_to_delete."grantCallId" 
                    AND gcss_existing."${fkColumnInGrantCallStageSettings}" = dtr.target_id 
              )
        );
    `);
    console.log("Deletion of conflicting 'grant_call_stage_settings' rows complete.");

    console.log(`Updating remaining rows in 'grant_call_stage_settings'.`);
    await queryRunner.query(`
        ${duplicatesToRemapCTE}
        UPDATE "grant_call_stage_settings" gcss_to_update
        SET "${fkColumnInGrantCallStageSettings}" = dtr.target_id
        FROM duplicates_to_remap dtr
        WHERE gcss_to_update."${fkColumnInGrantCallStageSettings}" = dtr.duplicate_id
          AND dtr.target_id IS NOT NULL;
    `);
    console.log("Re-parenting of remaining foreign keys in 'grant_call_stage_settings' complete.");
    
    console.log("Attempting to delete duplicate entries from 'workflow_step_definitions'.");
    await queryRunner.query(`
      DELETE FROM "workflow_step_definitions" a
      USING (
        SELECT MIN(id) as id, "workflowTemplateId", code
        FROM "workflow_step_definitions"
        WHERE code IS NOT NULL 
        GROUP BY "workflowTemplateId", code
        HAVING COUNT(*) > 1
      ) b
      WHERE a."workflowTemplateId" = b."workflowTemplateId"
        AND a.code = b.code
        AND a.id != b.id
    `);
    console.log("Deletion of duplicate entries from 'workflow_step_definitions' complete.");

    // ================= ENSURE WORKFLOW TEMPLATES EXIST =================
    console.log("Ensuring workflow templates exist for all entity types...");

    const workflowTemplates = [
      { name: 'Default Grant Program Workflow', entityType: 'PROGRAM' },
      { name: 'Default Grant Call Workflow', entityType: 'CALL' },
      { name: 'Default Grant Application Workflow', entityType: 'APPLICATION' }
    ];

    for (const template of workflowTemplates) {
      const existingTemplate = await queryRunner.query(
        `SELECT id FROM "workflow_templates" WHERE "name" = $1 AND "entityType" = $2 LIMIT 1`,
        [template.name, template.entityType]
      );

      if (existingTemplate.length === 0) {
        console.log(`Creating missing workflow template: ${template.name} for entity type: ${template.entityType}`);
        await queryRunner.query(
          `INSERT INTO "workflow_templates" ("name", "entityType") VALUES ($1, $2)`,
          [template.name, template.entityType]
        );
        console.log(`Successfully created workflow template: ${template.name}`);
      } else {
        console.log(`Workflow template already exists: ${template.name}`);
      }
    }
    console.log("Workflow template verification complete.");

    // ================= ENSURE WORKFLOW STEP DEFINITIONS EXIST =================
    console.log("Ensuring workflow step definitions exist for all templates...");

    // Get template IDs
    const templateResults = await queryRunner.query(`
      SELECT id, "entityType" FROM "workflow_templates"
      WHERE "entityType" IN ('PROGRAM', 'CALL', 'APPLICATION')
    `);

    const templateMap = new Map();
    templateResults.forEach(t => templateMap.set(t.entityType, t.id));

    // Define the step definitions that should exist
    const requiredSteps = [
      // Grant Program steps
      { templateType: 'PROGRAM', name: 'Open', code: 'GP_OPEN', sequenceNumber: 10, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'PROGRAM', name: 'Finalized', code: 'GP_FINALIZED', sequenceNumber: 100, transitionType: 'MANUAL', isTerminal: true },

      // Grant Call steps
      { templateType: 'CALL', name: 'Closed', code: 'GC_CLOSED', sequenceNumber: 10, transitionType: 'AUTOMATIC', isTerminal: true },
      { templateType: 'CALL', name: 'Open for Applications', code: 'GC_OPEN_FOR_APPLICATIONS', sequenceNumber: 20, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'CALL', name: 'Screening', code: 'GC_SCREENING', sequenceNumber: 30, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'CALL', name: 'Community Voting', code: 'GC_COMMUNITY_VOTING', sequenceNumber: 40, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'CALL', name: 'Onboarding', code: 'GC_ONBOARDING', sequenceNumber: 50, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'CALL', name: 'Final Community Voting', code: 'GC_FINAL_COMMUNITY_VOTING', sequenceNumber: 60, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'CALL', name: 'Finalized', code: 'GC_FINALIZED', sequenceNumber: 100, transitionType: 'MANUAL', isTerminal: true },

      // Grant Application steps
      { templateType: 'APPLICATION', name: 'Screening', code: 'GA_SCREENING', sequenceNumber: 10, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Qualification', code: 'GA_QUALIFICATION', sequenceNumber: 20, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Interview', code: 'GA_INTERVIEW', sequenceNumber: 30, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Due Diligence', code: 'GA_DUE_DILIGENCE', sequenceNumber: 40, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Town Hall', code: 'GA_TOWN_HALL', sequenceNumber: 50, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Final Qualification', code: 'GA_FINAL_QUALIFICATION', sequenceNumber: 60, transitionType: 'MANUAL', isTerminal: false },
    ];

    for (const step of requiredSteps) {
      const templateId = templateMap.get(step.templateType);
      if (!templateId) {
        console.log(`Template not found for type: ${step.templateType}, skipping step: ${step.code}`);
        continue;
      }

      const existingStep = await queryRunner.query(
        `SELECT id FROM "workflow_step_definitions" WHERE "workflowTemplateId" = $1 AND "code" = $2 LIMIT 1`,
        [templateId, step.code]
      );

      if (existingStep.length === 0) {
        console.log(`Creating missing workflow step definition: ${step.code} for template type: ${step.templateType}`);
        await queryRunner.query(
          `INSERT INTO "workflow_step_definitions" ("workflowTemplateId", "name", "code", "sequenceNumber", "transitionType", "isTerminal") VALUES ($1, $2, $3, $4, $5, $6)`,
          [templateId, step.name, step.code, step.sequenceNumber, step.transitionType, step.isTerminal]
        );
        console.log(`Successfully created workflow step definition: ${step.code}`);
      } else {
        console.log(`Workflow step definition already exists: ${step.code}`);
      }
    }
    console.log("Workflow step definition verification complete.");

    // ================= COMPREHENSIVE FIX FOR CORRUPTED WORKFLOW STEP DEFINITIONS =================
    console.log("Starting comprehensive workflow step definition corruption fix...");

    // First, let's see what we have
    const allTemplatesForFix = await queryRunner.query(`
      SELECT id, "entityType", name FROM "workflow_templates" ORDER BY id
    `);
    console.log('Current templates:', JSON.stringify(allTemplatesForFix, null, 2));

    const allStepsForFix = await queryRunner.query(`
      SELECT id, "workflowTemplateId", name, code FROM "workflow_step_definitions" ORDER BY "workflowTemplateId", id
    `);
    console.log('Current step definitions:', JSON.stringify(allStepsForFix, null, 2));

    // FIRST FIX: Correct the workflow template entity types
    console.log('Fixing workflow template entity types...');

    // Based on the names, fix the entity types
    for (const template of allTemplatesForFix) {
      let correctEntityType = null;
      if (template.name.includes('Program')) {
        correctEntityType = 'PROGRAM';
      } else if (template.name.includes('Call')) {
        correctEntityType = 'CALL';
      } else if (template.name.includes('Application')) {
        correctEntityType = 'APPLICATION';
      }

      if (correctEntityType && template.entityType !== correctEntityType) {
        console.log(`Updating template ${template.id} entity type: ${template.entityType} -> ${correctEntityType}`);
        await queryRunner.query(
          `UPDATE "workflow_templates" SET "entityType" = $1 WHERE id = $2`,
          [correctEntityType, template.id]
        );
      }
    }

    // Refresh template data after fixing entity types
    const updatedTemplatesForFix = await queryRunner.query(`
      SELECT id, "entityType", name FROM "workflow_templates" ORDER BY id
    `);
    console.log('Updated templates:', JSON.stringify(updatedTemplatesForFix, null, 2));

    // Create a map of entity types to template IDs
    const templateMapForFix = new Map();
    updatedTemplatesForFix.forEach(t => templateMapForFix.set(t.entityType, t.id));
    console.log('Template map:', Object.fromEntries(templateMapForFix));

    // Check what workflow states are currently referencing step definitions
    const workflowStatesWithSteps = await queryRunner.query(`
      SELECT ws.id as workflow_state_id, ws."currentStepDefinitionId", wsd.code, wsd.name, wsd."workflowTemplateId"
      FROM "workflow_state" ws
      LEFT JOIN "workflow_step_definitions" wsd ON ws."currentStepDefinitionId" = wsd.id
      WHERE ws."currentStepDefinitionId" IS NOT NULL
    `);
    console.log('Current workflow states with step definitions:', JSON.stringify(workflowStatesWithSteps, null, 2));

    // Check what grant_call_stage_settings are referencing step definitions
    const stageSettingsWithSteps = await queryRunner.query(`
      SELECT id, "workflowStepDefinitionId" FROM "grant_call_stage_settings" WHERE "workflowStepDefinitionId" IS NOT NULL
    `);
    console.log('Grant call stage settings with step definitions:', JSON.stringify(stageSettingsWithSteps, null, 2));

    // DIRECT FIX: Delete all existing step definitions and recreate them properly
    console.log('Deleting all corrupted workflow step definitions...');

    // First, set all workflow states to not reference any step definitions temporarily
    await queryRunner.query(`UPDATE "workflow_state" SET "currentStepDefinitionId" = NULL`);

    // For grant_call_stage_settings, we need to temporarily drop the NOT NULL constraint
    console.log('Temporarily dropping NOT NULL constraint on workflowStepDefinitionId...');
    await queryRunner.query(`ALTER TABLE "grant_call_stage_settings" ALTER COLUMN "workflowStepDefinitionId" DROP NOT NULL`);

    // Now we can clear grant_call_stage_settings references
    await queryRunner.query(`UPDATE "grant_call_stage_settings" SET "workflowStepDefinitionId" = NULL`);

    // Now delete all step definitions
    await queryRunner.query(`DELETE FROM "workflow_step_definitions"`);

    // Define the correct step definitions (using the same as requiredSteps but with different variable name)
    const correctSteps = [
      // Grant Program steps
      { templateType: 'PROGRAM', name: 'Open', code: 'GP_OPEN', sequenceNumber: 10, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'PROGRAM', name: 'Finalized', code: 'GP_FINALIZED', sequenceNumber: 100, transitionType: 'MANUAL', isTerminal: true },

      // Grant Call steps
      { templateType: 'CALL', name: 'Closed', code: 'GC_CLOSED', sequenceNumber: 10, transitionType: 'AUTOMATIC', isTerminal: true },
      { templateType: 'CALL', name: 'Open for Applications', code: 'GC_OPEN_FOR_APPLICATIONS', sequenceNumber: 20, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'CALL', name: 'Screening', code: 'GC_SCREENING', sequenceNumber: 30, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'CALL', name: 'Community Voting', code: 'GC_COMMUNITY_VOTING', sequenceNumber: 40, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'CALL', name: 'Onboarding', code: 'GC_ONBOARDING', sequenceNumber: 50, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'CALL', name: 'Final Community Voting', code: 'GC_FINAL_COMMUNITY_VOTING', sequenceNumber: 60, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'CALL', name: 'Finalized', code: 'GC_FINALIZED', sequenceNumber: 100, transitionType: 'MANUAL', isTerminal: true },

      // Grant Application steps
      { templateType: 'APPLICATION', name: 'Screening', code: 'GA_SCREENING', sequenceNumber: 10, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Qualification', code: 'GA_QUALIFICATION', sequenceNumber: 20, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Interview', code: 'GA_INTERVIEW', sequenceNumber: 30, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Due Diligence', code: 'GA_DUE_DILIGENCE', sequenceNumber: 40, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Town Hall', code: 'GA_TOWN_HALL', sequenceNumber: 50, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Final Qualification', code: 'GA_FINAL_QUALIFICATION', sequenceNumber: 60, transitionType: 'MANUAL', isTerminal: false },
    ];

    // Create all step definitions correctly
    const newStepMap = new Map(); // code -> new step definition id

    for (const step of correctSteps) {
      const templateId = templateMapForFix.get(step.templateType);
      if (!templateId) {
        console.log(`Template not found for type: ${step.templateType}, skipping step: ${step.code}`);
        continue;
      }

      console.log(`Creating workflow step definition: ${step.code} for template ID: ${templateId} (${step.templateType})`);
      const result = await queryRunner.query(
        `INSERT INTO "workflow_step_definitions" ("workflowTemplateId", "name", "code", "sequenceNumber", "transitionType", "isTerminal")
         VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
        [templateId, step.name, step.code, step.sequenceNumber, step.transitionType, step.isTerminal]
      );

      const newStepId = result[0].id;
      newStepMap.set(step.code, newStepId);
      console.log(`Created step definition ${step.code} with ID: ${newStepId}`);
    }

    // Now restore workflow state references where possible
    console.log('Restoring workflow state references...');

    for (const workflowState of workflowStatesWithSteps) {
      if (workflowState.code && newStepMap.has(workflowState.code)) {
        const newStepId = newStepMap.get(workflowState.code);
        console.log(`Restoring workflow state ${workflowState.workflow_state_id}: ${workflowState.code} -> step ID ${newStepId}`);
        await queryRunner.query(
          `UPDATE "workflow_state" SET "currentStepDefinitionId" = $1 WHERE id = $2`,
          [newStepId, workflowState.workflow_state_id]
        );
      } else {
        console.log(`Could not restore workflow state ${workflowState.workflow_state_id}: step code ${workflowState.code} not found in new definitions`);
      }
    }

    // Restore grant_call_stage_settings references where possible
    console.log('Restoring grant call stage settings references...');

    // Group stage settings by grantCallId to handle unique constraint
    const stageSettingsByGrantCall = new Map();
    for (const stageSetting of stageSettingsWithSteps) {
      // Get the grantCallId for this stage setting
      const grantCallResult = await queryRunner.query(
        `SELECT "grantCallId" FROM "grant_call_stage_settings" WHERE id = $1`,
        [stageSetting.id]
      );

      if (grantCallResult.length > 0) {
        const grantCallId = grantCallResult[0].grantCallId;
        if (!stageSettingsByGrantCall.has(grantCallId)) {
          stageSettingsByGrantCall.set(grantCallId, []);
        }
        stageSettingsByGrantCall.get(grantCallId).push(stageSetting);
      }
    }

    // Available CALL step definitions to distribute
    const callStepIds = [
      newStepMap.get('GC_CLOSED'),
      newStepMap.get('GC_OPEN_FOR_APPLICATIONS'),
      newStepMap.get('GC_SCREENING'),
      newStepMap.get('GC_COMMUNITY_VOTING'),
      newStepMap.get('GC_ONBOARDING'),
      newStepMap.get('GC_FINAL_COMMUNITY_VOTING'),
      newStepMap.get('GC_FINALIZED'),
    ].filter(id => id !== undefined);

    // Restore stage settings, ensuring no duplicates per grant call
    for (const [grantCallId, settings] of stageSettingsByGrantCall) {
      console.log(`Restoring ${settings.length} stage settings for grant call ${grantCallId}`);

      for (let i = 0; i < settings.length; i++) {
        const stageSetting = settings[i];
        // Use different step definitions to avoid unique constraint violation
        const stepId = callStepIds[i % callStepIds.length];

        if (stepId) {
          const stepCode = [...newStepMap.entries()].find(([code, id]) => id === stepId)?.[0];
          console.log(`Restoring grant call stage setting ${stageSetting.id}: -> step ID ${stepId} (${stepCode})`);
          await queryRunner.query(
            `UPDATE "grant_call_stage_settings" SET "workflowStepDefinitionId" = $1 WHERE id = $2`,
            [stepId, stageSetting.id]
          );
        } else {
          console.log(`Could not restore grant call stage setting ${stageSetting.id}: no step definition available`);
        }
      }
    }

    // Restore the NOT NULL constraint on workflowStepDefinitionId
    console.log('Restoring NOT NULL constraint on workflowStepDefinitionId...');
    await queryRunner.query(`ALTER TABLE "grant_call_stage_settings" ALTER COLUMN "workflowStepDefinitionId" SET NOT NULL`);

    console.log('Comprehensive workflow step definition corruption fix completed successfully.');
    // ================= END COMPREHENSIVE FIX FOR CORRUPTED WORKFLOW STEP DEFINITIONS =================

    // Add back constraints and indexes
    await queryRunner.query(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_e6f73eaa19ceafbff74c0cf469" ON "workflow_step_definitions" ("workflowTemplateId", "code") WHERE code IS NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD CONSTRAINT "FK_eef23d9c2f14c87587b3f5f20f5" FOREIGN KEY ("workflowStateId") REFERENCES "workflow_state"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );

    // Ensure required columns are NOT NULL
    await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "actionTopicId" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "workflowStateId" SET NOT NULL`);
  }

  // ... 'down' method remains unchanged ...
  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop constraints and indexes first
    await queryRunner.query(`ALTER TABLE "grant_application" DROP CONSTRAINT IF EXISTS "FK_eef23d9c2f14c87587b3f5f20f5"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);

    // Revert NOT NULL constraints
    await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "actionTopicId" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "workflowStateId" DROP NOT NULL`);

    console.warn("Warning: The 'down' migration for Migration1747816433087 does not re-create deleted duplicate data or revert foreign key re-parenting/unique conflict resolutions. It primarily reverts schema changes.");

    const enumDefinitions = [
        { name: "workflow_templates_entitytype_enum", values: "'PROGRAM', 'CALL', 'APPLICATION'" },
        { name: "workflow_step_definitions_code_enum", values: "'GP_OPEN', 'GP_FINALIZED', 'GC_CLOSED', 'GC_OPEN_FOR_APPLICATIONS', 'GC_SCREENING', 'GC_COMMUNITY_VOTING', 'GC_ONBOARDING', 'GC_FINAL_COMMUNITY_VOTING', 'GC_FINALIZED', 'GA_SCREENING', 'GA_QUALIFICATION', 'GA_INTERVIEW', 'GA_DUE_DILIGENCE', 'GA_TOWN_HALL', 'GA_FINAL_QUALIFICATION'" },
        { name: "workflow_step_definitions_transitiontype_enum", values: "'MANUAL', 'AUTOMATIC'" },
        { name: "grant_distribution_rules_type_enum", values: "'PERCENTAGE', 'FIXED_AMOUNT'" },
        { name: "workflow_state_status_enum", values: "'IN_PROGRESS', 'READY_FOR_NEXT_STEP', 'ACTION_REQUIRED', 'APPROVED', 'REJECTED', 'WITHDRAWN'" },
        { name: "grant_application_status_enum", values: "'OPEN', 'WITHDRAWN', 'REJECTED', 'APPROVED'" }
    ];

    for (const enumDef of enumDefinitions) {
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."${enumDef.name}" CASCADE`); 
        await queryRunner.query(`CREATE TYPE "public"."${enumDef.name}" AS ENUM(${enumDef.values})`);
    }
    
    const columnReversions = [
        { table: "workflow_templates", column: "entityType", oldEnum: "workflow_templates_entitytype_enum", newEnum: "WorkflowEntityType" },
        { table: "workflow_step_definitions", column: "code", oldEnum: "workflow_step_definitions_code_enum", newEnum: "StageCode" },
        { table: "workflow_step_definitions", column: "transitionType", oldEnum: "workflow_step_definitions_transitiontype_enum", newEnum: "StageTransitionType" },
        { table: "grant_distribution_rules", column: "type", oldEnum: "grant_distribution_rules_type_enum", newEnum: "DistributionType" },
        { table: "workflow_state", column: "status", oldEnum: "workflow_state_status_enum", newEnum: "WorkflowStatus" },
        { table: "grant_application", column: "status", oldEnum: "grant_application_status_enum", newEnum: "ApplicationStatus" }
    ];

    for (const rev of columnReversions) {
        const hasColumn = await queryRunner.hasColumn(rev.table, rev.column);
        if (hasColumn) {
            try {
                await queryRunner.query(
                    `ALTER TABLE "${rev.table}" ALTER COLUMN "${rev.column}" TYPE "public"."${rev.oldEnum}" USING "${rev.column}"::"text"::"public"."${rev.oldEnum}"`
                );
            } catch (e) {
                console.error(`Failed to revert column ${rev.table}.${rev.column} to enum ${rev.oldEnum}: ${(e as Error).message}. This might happen if data is incompatible with the old enum definition or if the old enum was not correctly (re)defined.`);
            }
        }
    }

    // Drop new enum types (that were created in 'up')
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."WorkflowEntityType" CASCADE`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."StageCode" CASCADE`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."StageTransitionType" CASCADE`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."DistributionType" CASCADE`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."WorkflowStatus" CASCADE`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."ApplicationStatus" CASCADE`);

    await queryRunner.query(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_e6f73eaa19ceafbff74c0cf469_old" ON "workflow_step_definitions" ("code", "workflowTemplateId") WHERE code IS NOT NULL`, 
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD CONSTRAINT "FK_eef23d9c2f14c87587b3f5f20f5" FOREIGN KEY ("workflowStateId") REFERENCES "workflow_state"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
  }
}