import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixWorkflowStepDefinitionCorruption1747920000000 implements MigrationInterface {
  name = 'FixWorkflowStepDefinitionCorruption1747920000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Starting workflow step definition corruption fix...');

    // First, let's see what we have
    const allTemplates = await queryRunner.query(`
      SELECT id, "entityType", name FROM "workflow_templates" ORDER BY id
    `);
    console.log('Current templates:', JSON.stringify(allTemplates, null, 2));

    const allSteps = await queryRunner.query(`
      SELECT id, "workflowTemplateId", name, code FROM "workflow_step_definitions" ORDER BY "workflowTemplateId", id
    `);
    console.log('Current step definitions:', JSON.stringify(allSteps, null, 2));

    // FIRST FIX: Correct the workflow template entity types
    console.log('Fixing workflow template entity types...');

    // Based on the names, fix the entity types
    for (const template of allTemplates) {
      let correctEntityType = null;
      if (template.name.includes('Program')) {
        correctEntityType = 'PROGRAM';
      } else if (template.name.includes('Call')) {
        correctEntityType = 'CALL';
      } else if (template.name.includes('Application')) {
        correctEntityType = 'APPLICATION';
      }

      if (correctEntityType && template.entityType !== correctEntityType) {
        console.log(`Updating template ${template.id} entity type: ${template.entityType} -> ${correctEntityType}`);
        await queryRunner.query(
          `UPDATE "workflow_templates" SET "entityType" = $1 WHERE id = $2`,
          [correctEntityType, template.id]
        );
      }
    }

    // Refresh template data after fixing entity types
    const updatedTemplates = await queryRunner.query(`
      SELECT id, "entityType", name FROM "workflow_templates" ORDER BY id
    `);
    console.log('Updated templates:', JSON.stringify(updatedTemplates, null, 2));

    // Create a map of entity types to template IDs
    const templateMap = new Map();
    updatedTemplates.forEach(t => templateMap.set(t.entityType, t.id));
    console.log('Template map:', Object.fromEntries(templateMap));

    // Check what workflow states are currently referencing step definitions
    const workflowStatesWithSteps = await queryRunner.query(`
      SELECT ws.id as workflow_state_id, ws."currentStepDefinitionId", wsd.code, wsd.name, wsd."workflowTemplateId"
      FROM "workflow_state" ws
      LEFT JOIN "workflow_step_definitions" wsd ON ws."currentStepDefinitionId" = wsd.id
      WHERE ws."currentStepDefinitionId" IS NOT NULL
    `);
    console.log('Current workflow states with step definitions:', JSON.stringify(workflowStatesWithSteps, null, 2));

    // Check what grant_call_stage_settings are referencing step definitions
    const stageSettingsWithSteps = await queryRunner.query(`
      SELECT id, "workflowStepDefinitionId" FROM "grant_call_stage_settings" WHERE "workflowStepDefinitionId" IS NOT NULL
    `);
    console.log('Grant call stage settings with step definitions:', JSON.stringify(stageSettingsWithSteps, null, 2));

    // DIRECT FIX: Delete all existing step definitions and recreate them properly
    console.log('Deleting all corrupted workflow step definitions...');

    // First, set all workflow states to not reference any step definitions temporarily
    await queryRunner.query(`UPDATE "workflow_state" SET "currentStepDefinitionId" = NULL`);

    // For grant_call_stage_settings, we need to temporarily drop the NOT NULL constraint
    console.log('Temporarily dropping NOT NULL constraint on workflowStepDefinitionId...');
    await queryRunner.query(`ALTER TABLE "grant_call_stage_settings" ALTER COLUMN "workflowStepDefinitionId" DROP NOT NULL`);

    // Now we can clear grant_call_stage_settings references
    await queryRunner.query(`UPDATE "grant_call_stage_settings" SET "workflowStepDefinitionId" = NULL`);

    // Now delete all step definitions
    await queryRunner.query(`DELETE FROM "workflow_step_definitions"`);

    // Define the correct step definitions
    const correctSteps = [
      // Grant Program steps
      { templateType: 'PROGRAM', name: 'Open', code: 'GP_OPEN', sequenceNumber: 10, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'PROGRAM', name: 'Finalized', code: 'GP_FINALIZED', sequenceNumber: 100, transitionType: 'MANUAL', isTerminal: true },
      
      // Grant Call steps
      { templateType: 'CALL', name: 'Closed', code: 'GC_CLOSED', sequenceNumber: 10, transitionType: 'AUTOMATIC', isTerminal: true },
      { templateType: 'CALL', name: 'Open for Applications', code: 'GC_OPEN_FOR_APPLICATIONS', sequenceNumber: 20, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'CALL', name: 'Screening', code: 'GC_SCREENING', sequenceNumber: 30, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'CALL', name: 'Community Voting', code: 'GC_COMMUNITY_VOTING', sequenceNumber: 40, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'CALL', name: 'Onboarding', code: 'GC_ONBOARDING', sequenceNumber: 50, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'CALL', name: 'Final Community Voting', code: 'GC_FINAL_COMMUNITY_VOTING', sequenceNumber: 60, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'CALL', name: 'Finalized', code: 'GC_FINALIZED', sequenceNumber: 100, transitionType: 'MANUAL', isTerminal: true },
      
      // Grant Application steps
      { templateType: 'APPLICATION', name: 'Screening', code: 'GA_SCREENING', sequenceNumber: 10, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Qualification', code: 'GA_QUALIFICATION', sequenceNumber: 20, transitionType: 'AUTOMATIC', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Interview', code: 'GA_INTERVIEW', sequenceNumber: 30, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Due Diligence', code: 'GA_DUE_DILIGENCE', sequenceNumber: 40, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Town Hall', code: 'GA_TOWN_HALL', sequenceNumber: 50, transitionType: 'MANUAL', isTerminal: false },
      { templateType: 'APPLICATION', name: 'Final Qualification', code: 'GA_FINAL_QUALIFICATION', sequenceNumber: 60, transitionType: 'MANUAL', isTerminal: false },
    ];

    // Create all step definitions correctly
    const newStepMap = new Map(); // code -> new step definition id
    
    for (const step of correctSteps) {
      const templateId = templateMap.get(step.templateType);
      if (!templateId) {
        console.log(`Template not found for type: ${step.templateType}, skipping step: ${step.code}`);
        continue;
      }

      console.log(`Creating workflow step definition: ${step.code} for template ID: ${templateId} (${step.templateType})`);
      const result = await queryRunner.query(
        `INSERT INTO "workflow_step_definitions" ("workflowTemplateId", "name", "code", "sequenceNumber", "transitionType", "isTerminal") 
         VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
        [templateId, step.name, step.code, step.sequenceNumber, step.transitionType, step.isTerminal]
      );
      
      const newStepId = result[0].id;
      newStepMap.set(step.code, newStepId);
      console.log(`Created step definition ${step.code} with ID: ${newStepId}`);
    }

    // Now restore workflow state references where possible
    console.log('Restoring workflow state references...');

    for (const workflowState of workflowStatesWithSteps) {
      if (workflowState.code && newStepMap.has(workflowState.code)) {
        const newStepId = newStepMap.get(workflowState.code);
        console.log(`Restoring workflow state ${workflowState.workflow_state_id}: ${workflowState.code} -> step ID ${newStepId}`);
        await queryRunner.query(
          `UPDATE "workflow_state" SET "currentStepDefinitionId" = $1 WHERE id = $2`,
          [newStepId, workflowState.workflow_state_id]
        );
      } else {
        console.log(`Could not restore workflow state ${workflowState.workflow_state_id}: step code ${workflowState.code} not found in new definitions`);
      }
    }

    // Restore grant_call_stage_settings references where possible
    console.log('Restoring grant call stage settings references...');

    // Group stage settings by grantCallId to handle unique constraint
    const stageSettingsByGrantCall = new Map();
    for (const stageSetting of stageSettingsWithSteps) {
      // Get the grantCallId for this stage setting
      const grantCallResult = await queryRunner.query(
        `SELECT "grantCallId" FROM "grant_call_stage_settings" WHERE id = $1`,
        [stageSetting.id]
      );

      if (grantCallResult.length > 0) {
        const grantCallId = grantCallResult[0].grantCallId;
        if (!stageSettingsByGrantCall.has(grantCallId)) {
          stageSettingsByGrantCall.set(grantCallId, []);
        }
        stageSettingsByGrantCall.get(grantCallId).push(stageSetting);
      }
    }

    // Available CALL step definitions to distribute
    const callStepIds = [
      newStepMap.get('GC_CLOSED'),
      newStepMap.get('GC_OPEN_FOR_APPLICATIONS'),
      newStepMap.get('GC_SCREENING'),
      newStepMap.get('GC_COMMUNITY_VOTING'),
      newStepMap.get('GC_ONBOARDING'),
      newStepMap.get('GC_FINAL_COMMUNITY_VOTING'),
      newStepMap.get('GC_FINALIZED'),
    ].filter(id => id !== undefined);

    // Restore stage settings, ensuring no duplicates per grant call
    for (const [grantCallId, settings] of stageSettingsByGrantCall) {
      console.log(`Restoring ${settings.length} stage settings for grant call ${grantCallId}`);

      for (let i = 0; i < settings.length; i++) {
        const stageSetting = settings[i];
        // Use different step definitions to avoid unique constraint violation
        const stepId = callStepIds[i % callStepIds.length];

        if (stepId) {
          const stepCode = [...newStepMap.entries()].find(([code, id]) => id === stepId)?.[0];
          console.log(`Restoring grant call stage setting ${stageSetting.id}: -> step ID ${stepId} (${stepCode})`);
          await queryRunner.query(
            `UPDATE "grant_call_stage_settings" SET "workflowStepDefinitionId" = $1 WHERE id = $2`,
            [stepId, stageSetting.id]
          );
        } else {
          console.log(`Could not restore grant call stage setting ${stageSetting.id}: no step definition available`);
        }
      }
    }

    // Restore the NOT NULL constraint on workflowStepDefinitionId
    console.log('Restoring NOT NULL constraint on workflowStepDefinitionId...');
    await queryRunner.query(`ALTER TABLE "grant_call_stage_settings" ALTER COLUMN "workflowStepDefinitionId" SET NOT NULL`);

    console.log('Workflow step definition corruption fix completed successfully.');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('This migration cannot be safely reverted as it fixes data corruption.');
    console.log('Manual intervention would be required to restore the previous corrupted state.');
  }
}
