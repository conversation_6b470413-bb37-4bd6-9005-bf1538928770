import { validate } from 'class-validator';
import { UpdateGrantCallStatusDto } from './update-grant-call-status.dto';
import { WorkflowStatus } from '../../workflow/enums/workflow-status.enum';

describe('UpdateGrantCallStatusDto', () => {
  let dto: UpdateGrantCallStatusDto;

  beforeEach(() => {
    dto = new UpdateGrantCallStatusDto();
  });

  describe('status field validation', () => {
    it('should pass validation with CLOSED status and reason', async () => {
      dto.status = WorkflowStatus.CLOSED;
      dto.reason = 'Valid reason for closing';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with CLOSED status and no reason', async () => {
      dto.status = WorkflowStatus.CLOSED;
      // dto.reason is undefined (optional)

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation with IN_PROGRESS status', async () => {
      dto.status = WorkflowStatus.IN_PROGRESS as any;
      dto.reason = 'Valid reason';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('status');
      expect(errors[0].constraints?.isIn).toContain(
        `Grant Call status can only be set to '${WorkflowStatus.CLOSED}'. Other status changes should use transition endpoints.`
      );
    });

    it('should fail validation with APPROVED status', async () => {
      dto.status = WorkflowStatus.APPROVED as any;
      dto.reason = 'Valid reason';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('status');
      expect(errors[0].constraints?.isIn).toContain(
        `Grant Call status can only be set to '${WorkflowStatus.CLOSED}'. Other status changes should use transition endpoints.`
      );
    });

    it('should fail validation with REJECTED status', async () => {
      dto.status = WorkflowStatus.REJECTED as any;
      dto.reason = 'Valid reason';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('status');
      expect(errors[0].constraints?.isIn).toContain(
        `Grant Call status can only be set to '${WorkflowStatus.CLOSED}'. Other status changes should use transition endpoints.`
      );
    });

    it('should fail validation with WITHDRAWN status', async () => {
      dto.status = WorkflowStatus.WITHDRAWN as any;
      dto.reason = 'Valid reason';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('status');
      expect(errors[0].constraints?.isIn).toContain(
        `Grant Call status can only be set to '${WorkflowStatus.CLOSED}'. Other status changes should use transition endpoints.`
      );
    });

    it('should fail validation with empty status', async () => {
      dto.reason = 'Valid reason';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('status');
      expect(errors[0].constraints?.isNotEmpty).toBeDefined();
    });

    it('should fail validation with null status', async () => {
      dto.status = null as any;
      dto.reason = 'Valid reason';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('status');
      expect(errors[0].constraints?.isNotEmpty).toBeDefined();
    });

    it('should fail validation with invalid enum value', async () => {
      dto.status = 'INVALID_STATUS' as any;
      dto.reason = 'Valid reason';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('status');
      expect(errors[0].constraints?.isEnum).toBeDefined();
    });
  });

  describe('reason field validation', () => {
    beforeEach(() => {
      dto.status = WorkflowStatus.CLOSED;
    });

    it('should pass validation with valid reason', async () => {
      dto.reason = 'Grant call funding cycle completed successfully';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with maximum length reason (1000 chars)', async () => {
      dto.reason = 'a'.repeat(1000);

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with undefined reason (optional)', async () => {
      // dto.reason is undefined by default and that's now valid

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation with empty string reason', async () => {
      dto.reason = '';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('reason');
      expect(errors[0].constraints?.isString).toBeDefined();
    });

    it('should pass validation with null reason (treated as undefined)', async () => {
      dto.reason = null as any;

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation with reason exceeding maximum length (1001 chars)', async () => {
      dto.reason = 'a'.repeat(1001);

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('reason');
      expect(errors[0].constraints?.maxLength).toBeDefined();
    });

    it('should fail validation with non-string reason', async () => {
      dto.reason = 123 as any;

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('reason');
      expect(errors[0].constraints?.isString).toBeDefined();
    });

    it('should pass validation with whitespace-only reason (trimmed)', async () => {
      dto.reason = '   Valid reason with spaces   ';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('combined validation', () => {
    it('should fail validation when status is invalid and reason is empty string', async () => {
      dto.status = WorkflowStatus.APPROVED as any;
      dto.reason = '';

      const errors = await validate(dto);
      expect(errors).toHaveLength(2);

      const statusError = errors.find(error => error.property === 'status');
      const reasonError = errors.find(error => error.property === 'reason');

      expect(statusError).toBeDefined();
      expect(reasonError).toBeDefined();
    });

    it('should pass validation with valid status and no reason', async () => {
      dto.status = WorkflowStatus.CLOSED;
      // dto.reason is undefined (optional)

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with all valid fields', async () => {
      dto.status = WorkflowStatus.CLOSED;
      dto.reason = 'Grant call completed successfully with all objectives met';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });
});
