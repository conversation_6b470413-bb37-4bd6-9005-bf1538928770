import { ApiProperty } from '@nestjs/swagger';
import { Grant<PERSON>allBaseDto } from './grant-call-base.dto';
import { GrantCallStageCode, StageCode } from '../../workflow/enums/stage-code.enum';
import { UserDto } from '../../auth/dto';

export class FindOneGrantCallBaseDto extends GrantCallBaseDto {
  @ApiProperty({
    description: 'Unique slug identifying the Grant Call.',
    example: 'grant-call-to-the-moon-hdoija',
  })
  grantCallSlug: string;

  @ApiProperty({ type: UserDto, description: 'Details of the user who created the grant call.' })
  createdBy: UserDto;

  @ApiProperty({ description: 'Number of applications submitted to this grant call.', example: 12 })
  grantApplicationsCount: number;

  @ApiProperty({
    description: 'The unique code identifying the current workflow stage.',
    enum: GrantCallStageCode,
    enumName: 'GrantCallStageCode',
    example: GrantCallStageCode.OPEN_FOR_APPLICATIONS,
    nullable: false,
  })
  status: StageCode;

  @ApiProperty({
    example: '2021-09-01T00:00:00.000Z',
    description: 'The last updated date of the grant call.',
  })
  updatedAt: Date;

  @ApiProperty({
    example: true,
    description: 'Tells if user is able to change grant call stage manually',
  })
  isAbleToChangeStageManually: boolean;
}
