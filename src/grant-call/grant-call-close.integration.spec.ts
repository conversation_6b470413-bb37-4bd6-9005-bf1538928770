import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON>allController } from './grant-call.controller';
import { GrantCallService } from './grant-call.service';
import { GrantApplicationService } from '../grant-application/grant-application.service';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';
import { UpdateGrantCallStatusDto } from './dto';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { StageCode } from '../workflow/enums/stage-code.enum';
import { WorkflowEntityType } from '../workflow/enums/workflow-entity-type.enum';
import { NotFoundException, UnprocessableEntityException } from '@nestjs/common';

describe('Grant Call Close - Integration Tests', () => {
  let controller: GrantCallController;

  const mockWorkflowTransitionService = {
    updateStateStatus: jest.fn(),
    setSpecificStage: jest.fn(),
    transitionSingleStateToNextStep: jest.fn(),
    findWorkflowStateByEntity: jest.fn(),
    bulkTransitionReadyApplicationsForCall: jest.fn(),
  };

  const mockGrantCallRepository = {
    findOne: jest.fn(),
    manager: {
      transaction: jest.fn(),
    },
  };

  const mockGrantCallService = {
    updateGrantCallStatus: jest.fn(),
    create: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    transitionGrantCallToNextStep: jest.fn(),
    bulkTransitionReadyApplicationsAtDueDiligence: jest.fn(),
    advanceGrantCallFromScreening: jest.fn(),
    advanceGrantCallFromOnboarding: jest.fn(),
    getCallStageDefinitions: jest.fn(),
    getCallStagesWithCounts: jest.fn(),
    getApplicationStageSummaries: jest.fn(),
  };

  const mockGrantApplicationService = {
    findGrantApplications: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GrantCallController],
      providers: [
        {
          provide: GrantCallService,
          useValue: mockGrantCallService,
        },
        {
          provide: GrantApplicationService,
          useValue: mockGrantApplicationService,
        },
        {
          provide: WorkflowTransitionService,
          useValue: mockWorkflowTransitionService,
        },
      ],
    }).compile();

    controller = module.get<GrantCallController>(GrantCallController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Grant Call Close Flow', () => {
    const mockSlug = 'test-grant-call-slug';
    const mockGrantCallId = 123;
    const mockDto: UpdateGrantCallStatusDto = {
      status: WorkflowStatus.CLOSED,
      reason: 'Grant call funding cycle completed',
    };

    it('should successfully complete the entire close flow', async () => {
      // Mock the complete flow
      const mockGrantCall = {
        id: mockGrantCallId,
        grantCallSlug: mockSlug,
        workflowState: {
          status: WorkflowStatus.IN_PROGRESS,
        },
      };

      const mockStatusUpdateResult = {
        status: WorkflowStatus.CLOSED,
        stepDefinitionCode: StageCode.GC_FINALIZED,
      };

      // Setup mocks for the actual service implementation
      mockGrantCallRepository.findOne.mockResolvedValue(mockGrantCall);
      mockGrantCallRepository.manager.transaction.mockImplementation(async (callback) => {
        return await callback({});
      });
      mockWorkflowTransitionService.updateStateStatus.mockResolvedValue(mockStatusUpdateResult);
      mockWorkflowTransitionService.setSpecificStage.mockResolvedValue(mockStatusUpdateResult);

      // Use the real service implementation
      mockGrantCallService.updateGrantCallStatus.mockImplementation(async (slug, status) => {
        // Simulate the real service logic
        if (slug !== mockSlug) {
          throw new NotFoundException(`Grant Call with slug "${slug}" not found.`);
        }

        if (mockGrantCall.workflowState.status === WorkflowStatus.CLOSED) {
          throw new UnprocessableEntityException(
            `Cannot change Grant Call status from terminal status '${WorkflowStatus.CLOSED}'.`,
          );
        }

        // Simulate successful update
        await mockWorkflowTransitionService.updateStateStatus(WorkflowEntityType.CALL, mockGrantCallId, status, {});

        return await mockWorkflowTransitionService.setSpecificStage(
          WorkflowEntityType.CALL,
          mockGrantCallId,
          StageCode.GC_FINALIZED,
          {},
        );
      });

      // Execute the complete flow
      const result = await controller.setGrantCallStatusClosed(mockSlug, mockDto);

      // Verify the complete flow
      expect(mockGrantCallService.updateGrantCallStatus).toHaveBeenCalledWith(mockSlug, WorkflowStatus.CLOSED);

      expect(mockWorkflowTransitionService.updateStateStatus).toHaveBeenCalledWith(
        WorkflowEntityType.CALL,
        mockGrantCallId,
        WorkflowStatus.CLOSED,
        {},
      );

      expect(mockWorkflowTransitionService.setSpecificStage).toHaveBeenCalledWith(
        WorkflowEntityType.CALL,
        mockGrantCallId,
        StageCode.GC_FINALIZED,
        {},
      );

      expect(result).toEqual(mockStatusUpdateResult);
    });

    it('should handle grant call not found error in complete flow', async () => {
      mockGrantCallService.updateGrantCallStatus.mockImplementation(async (slug) => {
        throw new NotFoundException(`Grant Call with slug "${slug}" not found.`);
      });

      await expect(controller.setGrantCallStatusClosed('non-existent-slug', mockDto)).rejects.toThrow(
        NotFoundException,
      );

      expect(mockWorkflowTransitionService.updateStateStatus).not.toHaveBeenCalled();
      expect(mockWorkflowTransitionService.setSpecificStage).not.toHaveBeenCalled();
    });

    it('should handle terminal status error in complete flow', async () => {
      mockGrantCallService.updateGrantCallStatus.mockImplementation(async () => {
        throw new UnprocessableEntityException(
          `Cannot change Grant Call status from terminal status '${WorkflowStatus.CLOSED}'.`,
        );
      });

      await expect(controller.setGrantCallStatusClosed(mockSlug, mockDto)).rejects.toThrow(
        UnprocessableEntityException,
      );

      expect(mockWorkflowTransitionService.updateStateStatus).not.toHaveBeenCalled();
      expect(mockWorkflowTransitionService.setSpecificStage).not.toHaveBeenCalled();
    });

    it('should handle workflow transition service errors in complete flow', async () => {
      const workflowError = new Error('Failed to update workflow status');

      mockGrantCallService.updateGrantCallStatus.mockImplementation(async () => {
        throw workflowError;
      });

      await expect(controller.setGrantCallStatusClosed(mockSlug, mockDto)).rejects.toThrow(workflowError);
    });

    it('should validate the complete API contract', async () => {
      const mockResponse = {
        status: WorkflowStatus.CLOSED,
        stepDefinitionCode: StageCode.GC_FINALIZED,
      };

      mockGrantCallService.updateGrantCallStatus.mockResolvedValue(mockResponse);

      const result = await controller.setGrantCallStatusClosed(mockSlug, mockDto);

      // Verify API contract
      expect(result).toHaveProperty('status', WorkflowStatus.CLOSED);
      expect(result).toHaveProperty('stepDefinitionCode', StageCode.GC_FINALIZED);

      // Verify service was called with correct parameters
      expect(mockGrantCallService.updateGrantCallStatus).toHaveBeenCalledWith(mockSlug, WorkflowStatus.CLOSED);
    });

    it('should work without a reason field', async () => {
      const mockDtoWithoutReason: UpdateGrantCallStatusDto = {
        status: WorkflowStatus.CLOSED,
      };

      const mockResponse = {
        status: WorkflowStatus.CLOSED,
        stepDefinitionCode: StageCode.GC_FINALIZED,
      };

      mockGrantCallService.updateGrantCallStatus.mockResolvedValue(mockResponse);

      const result = await controller.setGrantCallStatusClosed(mockSlug, mockDtoWithoutReason);

      // Verify API contract
      expect(result).toEqual(mockResponse);

      // Verify service was called with correct parameters
      expect(mockGrantCallService.updateGrantCallStatus).toHaveBeenCalledWith(mockSlug, WorkflowStatus.CLOSED);
    });
  });
});
