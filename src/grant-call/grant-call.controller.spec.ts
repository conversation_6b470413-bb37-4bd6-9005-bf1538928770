import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON>allController } from './grant-call.controller';
import { GrantCallService } from './grant-call.service';
import { GrantApplicationService } from '../grant-application/grant-application.service';
import { UpdateGrantCallStatusDto } from './dto';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { StageCode } from '../workflow/enums/stage-code.enum';

describe('GrantCallController - Close Functionality', () => {
  let controller: GrantCallController;
  let grantCallService: jest.Mocked<GrantCallService>;

  const mockGrantCallService = {
    updateGrantCallStatus: jest.fn(),
    create: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    transitionGrantCallToNextStep: jest.fn(),
    bulkTransitionReadyApplicationsAtDueDiligence: jest.fn(),
    advanceGrantCallFromScreening: jest.fn(),
    advanceGrantCallFromOnboarding: jest.fn(),
    getCallStageDefinitions: jest.fn(),
    getCallStagesWithCounts: jest.fn(),
    getApplicationStageSummaries: jest.fn(),
  };

  const mockGrantApplicationService = {
    findGrantApplications: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GrantCallController],
      providers: [
        {
          provide: GrantCallService,
          useValue: mockGrantCallService,
        },
        {
          provide: GrantApplicationService,
          useValue: mockGrantApplicationService,
        },
      ],
    }).compile();

    controller = module.get<GrantCallController>(GrantCallController);
    grantCallService = module.get(GrantCallService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('setGrantCallStatusClosed', () => {
    const mockSlug = 'test-grant-call-slug';
    const mockDto: UpdateGrantCallStatusDto = {
      status: WorkflowStatus.CLOSED,
      reason: 'Grant call funding cycle completed',
    };
    const mockResponse: SingleTransitionResponseDto = {
      status: WorkflowStatus.CLOSED,
      stepDefinitionCode: StageCode.GC_FINALIZED,
    };

    it('should successfully close grant call', async () => {
      grantCallService.updateGrantCallStatus.mockResolvedValue(mockResponse);

      const result = await controller.setGrantCallStatusClosed(mockSlug, mockDto);

      expect(grantCallService.updateGrantCallStatus).toHaveBeenCalledWith(
        mockSlug,
        WorkflowStatus.CLOSED,
        mockDto.reason,
        1, // hardcoded userId in controller
      );
      expect(result).toEqual(mockResponse);
    });

    it('should pass correct parameters to service', async () => {
      grantCallService.updateGrantCallStatus.mockResolvedValue(mockResponse);

      await controller.setGrantCallStatusClosed(mockSlug, mockDto);

      expect(grantCallService.updateGrantCallStatus).toHaveBeenCalledTimes(1);
      expect(grantCallService.updateGrantCallStatus).toHaveBeenCalledWith(
        mockSlug,
        WorkflowStatus.CLOSED,
        'Grant call funding cycle completed',
        1,
      );
    });

    it('should propagate service errors', async () => {
      const serviceError = new Error('Grant call not found');
      grantCallService.updateGrantCallStatus.mockRejectedValue(serviceError);

      await expect(controller.setGrantCallStatusClosed(mockSlug, mockDto)).rejects.toThrow(serviceError);

      expect(grantCallService.updateGrantCallStatus).toHaveBeenCalledWith(
        mockSlug,
        WorkflowStatus.CLOSED,
        mockDto.reason,
        1,
      );
    });

    it('should handle different reasons correctly', async () => {
      const customDto: UpdateGrantCallStatusDto = {
        status: WorkflowStatus.CLOSED,
        reason: 'Emergency closure due to budget constraints',
      };
      grantCallService.updateGrantCallStatus.mockResolvedValue(mockResponse);

      await controller.setGrantCallStatusClosed(mockSlug, customDto);

      expect(grantCallService.updateGrantCallStatus).toHaveBeenCalledWith(
        mockSlug,
        WorkflowStatus.CLOSED,
        'Emergency closure due to budget constraints',
        1,
      );
    });
  });
});
