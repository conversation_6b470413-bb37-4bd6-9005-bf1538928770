import { Injectable, Logger, NotFoundException, UnprocessableEntityException } from '@nestjs/common';
import {
  CreateGrantCallDto,
  GrantCallBaseDto,
  FindOneGrantCallResponseDto,
  UpdateGrantCallDto,
  ConditionalBulkTransitionResponseDto,
  GrantCallDetailResponseDto,
  GrantCallApplicationStagesResponseDto,
} from './dto';
import { InjectRepository } from '@nestjs/typeorm';
import { GrantProgram } from '../grant-program/entities/grant-program.entity';
import { EntityManager, In, Repository } from 'typeorm';
import { GrantCall } from './entities/grant-call.entity';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import {
  ALL_POSSIBLE_SETTING_STAGES,
  BULK_ACTION_APP_SOURCE_MAP,
  GrantCallTimingProperties,
  REQUIRED_TIMING_STAGES,
  STAGE_TIMING_EXPECTATION,
  STAGE_URL_DTO_KEYS,
  StageTimingType,
} from './grant-call.constants';
import { GrantCallStageSetting } from './entities/grant-call-stage-setting.entity';
import { GrantCallStageCode, StageCode } from '../workflow/enums/stage-code.enum';
import { GrantDistributionRule } from './entities/grant-distribution-rule.entity';
import { DistributionType } from './enums/distribution-type.enum';
import { generateUniqueSlug } from '../utils/slugify.util';
import { GrantCallMapper } from './grant-call.mapper';
import { WorkflowState } from '../workflow/entities/workflow-state.entity';
import { WorkflowEntityType } from '../workflow/enums/workflow-entity-type.enum';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';
import { formatStageCode } from '../utils/formatting.util';
import { BulkTransitionResponseDto } from '../workflow/dto/bulk-transition-response.dto';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { WorkflowService } from '../workflow/workflow.service';
import { StageDefinitionResponseDto } from '../workflow/dto/stage-definition.response.dto';
import { StageWithCountResponseDto } from '../workflow/dto/stage-with-count.response.dto';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { GrantProgramService } from '../grant-program/grant-program.service';
import { WorkflowStatus } from 'src/workflow/enums/workflow-status.enum';

@Injectable()
export class GrantCallService {
  private readonly logger = new Logger(GrantCallService.name);

  constructor(
    @InjectRepository(WorkflowStepDefinition)
    private readonly stepDefinitionRepository: Repository<WorkflowStepDefinition>,
    @InjectRepository(GrantCall)
    private readonly grantCallRepository: Repository<GrantCall>,
    private readonly workflowTransitionService: WorkflowTransitionService,
    private readonly grantCallMapper: GrantCallMapper,
    private readonly workflowService: WorkflowService,
  ) {}

  async create(createGrantCallDto: CreateGrantCallDto, requestingUserId: number): Promise<{ grantCallSlug: string }> {
    return this.grantCallRepository.manager.transaction(async (transactionalEntityManager) => {
      const { grantProgram, stepDefMap } = await this.getProgramAndStageDefinitions(
        createGrantCallDto.grantProgramSlug,
        ALL_POSSIBLE_SETTING_STAGES,
        transactionalEntityManager,
      );

      const grantCallRepo = transactionalEntityManager.getRepository(GrantCall);
      const wfStateRepo = transactionalEntityManager.getRepository(WorkflowState);
      const stepDefRepo = transactionalEntityManager.getRepository(WorkflowStepDefinition);
      const wfTemplateRepo = transactionalEntityManager.getRepository(WorkflowTemplate);

      const grantCallSlug: string = await generateUniqueSlug(
        createGrantCallDto.name,
        async (slugToCheck) => grantCallRepo.exists({ where: { grantCallSlug: slugToCheck } }),
        'grant-call',
      );

      const defaultClosedStepDef = await stepDefRepo.findOneBy({ code: StageCode.GC_CLOSED });
      const workflowTemplate = await wfTemplateRepo.findOneBy({
        entityType: WorkflowEntityType.CALL,
      });

      const initialWorkflowState = wfStateRepo.create({
        workflowTemplate: workflowTemplate,
        currentStepDefinitionId: defaultClosedStepDef.id,
        currentStepDefinition: defaultClosedStepDef,
        currentStepTransitionedAt: new Date(),
        currentStepEndsAt: null,
      });

      await wfStateRepo.save(initialWorkflowState);

      const grantCall = grantCallRepo.create({
        grantProgram,
        name: createGrantCallDto.name,
        grantCallSlug,
        description: createGrantCallDto.description,
        businessCategory: createGrantCallDto.businessCategory,
        categories: createGrantCallDto.categories,
        totalGrantAmount: createGrantCallDto.totalGrantAmount,
        createdById: requestingUserId,
        workflowState: initialWorkflowState,
        stageSettings: this.buildStageSettings(createGrantCallDto, stepDefMap),
        distributionRules: this.buildDistributionRules(createGrantCallDto),
      });

      await grantCallRepo.save(grantCall);

      return {
        grantCallSlug,
      };
    });
  }

  async findOne(slug: string): Promise<FindOneGrantCallResponseDto> {
    const grantCall = await this.grantCallRepository.findOne({
      where: { grantCallSlug: slug },
      relations: {
        stageSettings: { workflowStepDefinition: true },
        workflowState: { currentStepDefinition: true, workflowTemplate: { steps: true } },
        grantProgram: {
          grantProgramCoordinator: true,
          // grantCalls: { workflowState: { currentStepDefinition: true, workflowTemplate: { steps: true } } },
          workflowState: { currentStepDefinition: true, workflowTemplate: { steps: true } },
        },
        distributionRules: true,
        createdBy: true,
      },
      order: {
        distributionRules: { id: 'ASC' },
      },
    });

    if (!grantCall) {
      throw new NotFoundException(`Grant Call with slug "${slug}" not found`);
    }

    return {
      ...(await this.grantCallMapper.mapGrantCallToDetailDto(grantCall)),
      grantProgram: GrantProgramService.createDtoFromGrantProgram(grantCall.grantProgram),
    };
  }

  async update(grantCallSlug: string, updateDto: UpdateGrantCallDto): Promise<GrantCallDetailResponseDto> {
    return this.grantCallRepository.manager.transaction(async (transactionalEntityManager) => {
      const grantCallRepo = transactionalEntityManager.getRepository(GrantCall);

      const grantCall = await grantCallRepo.findOne({
        where: { grantCallSlug },
        relations: ['grantProgram', 'stageSettings', 'stageSettings.workflowStepDefinition'],
      });

      if (!grantCall) {
        throw new NotFoundException(`Grant Call with slug '${grantCallSlug}' not found`);
      }

      grantCall.name = updateDto.name;
      grantCall.description = updateDto.description;

      if (grantCall.stageSettings && grantCall.stageSettings.length > 0) {
        for (const stageSetting of grantCall.stageSettings) {
          const stageCode = stageSetting.workflowStepDefinition.code;
          const dtoKey = STAGE_URL_DTO_KEYS[stageCode];

          if (dtoKey && updateDto[dtoKey] !== undefined) {
            stageSetting.stageUrl = updateDto[dtoKey];
          }
        }
      }

      await grantCallRepo.save(grantCall);

      return {
        grantCallSlug: grantCall.grantCallSlug,
      };
    });
  }

  async transitionGrantCallToNextStep(grantCallSlug: string): Promise<SingleTransitionResponseDto> {
    const grantCall = await this.grantCallRepository.findOne({ where: { grantCallSlug } });

    if (!grantCall) {
      throw new NotFoundException('Grant Call not found');
    }

    return this.workflowTransitionService.transitionSingleStateToNextStep(WorkflowEntityType.CALL, grantCall.id);
  }

  async bulkTransitionReadyApplicationsAtDueDiligence(grantCallSlug: string): Promise<BulkTransitionResponseDto> {
    const grantCallId = await this.getGrantCallIdBySlug(grantCallSlug);

    const applicationSourceStepId = await this.deriveApplicationSourceStepId(grantCallId, StageCode.GA_DUE_DILIGENCE);

    return this.workflowTransitionService.bulkTransitionReadyApplicationsForCall(grantCallId, applicationSourceStepId);
  }

  async advanceGrantCallFromScreening(grantCallSlug: string): Promise<ConditionalBulkTransitionResponseDto> {
    return await this.conditionallyAdvanceGrantCallStage(grantCallSlug, StageCode.GA_SCREENING);
  }

  async advanceGrantCallFromOnboarding(grantCallSlug: string): Promise<ConditionalBulkTransitionResponseDto> {
    return await this.conditionallyAdvanceGrantCallStage(grantCallSlug, StageCode.GA_TOWN_HALL);
  }

  private async conditionallyAdvanceGrantCallStage(
    grantCallSlug: string,
    stage: StageCode,
  ): Promise<ConditionalBulkTransitionResponseDto> {
    const grantCallId = await this.getGrantCallIdBySlug(grantCallSlug);

    const applicationSourceStepId = await this.deriveApplicationSourceStepId(grantCallId, stage);

    const appTransitionResult = await this.workflowTransitionService.bulkTransitionReadyApplicationsForCall(
      grantCallId,
      applicationSourceStepId,
    );

    const updatedGrantCallWorkflowState = await this.workflowTransitionService.transitionSingleStateToNextStep(
      WorkflowEntityType.CALL,
      grantCallId,
    );

    return {
      transitionedCount: appTransitionResult.transitionedCount,
      grantCallNewStage: updatedGrantCallWorkflowState.stepDefinitionCode as unknown as GrantCallStageCode,
    };
  }

  private async deriveApplicationSourceStepId(grantCallId: number, applicationSourceStage: StageCode): Promise<number> {
    const { currentStepDefinition } = await this.workflowTransitionService.findWorkflowStateByEntity(
      WorkflowEntityType.CALL,
      grantCallId,
    );

    const { code } = currentStepDefinition;

    const targetApplicationCodes = BULK_ACTION_APP_SOURCE_MAP[code];

    if (!targetApplicationCodes.length || !targetApplicationCodes.includes(applicationSourceStage)) {
      throw new UnprocessableEntityException(
        `Bulk transition action targeting applications in stage '${formatStageCode(applicationSourceStage)}' is not permitted while the grant call is in stage '${formatStageCode(code)}'.`,
      );
    }

    const applicationStepDef = await this.stepDefinitionRepository.findOne({
      where: { code: applicationSourceStage },
      select: ['id', 'code'],
    });

    return applicationStepDef.id;
  }

  private async getGrantCallIdBySlug(grantCallSlug: string): Promise<number> {
    const grantCall = await this.grantCallRepository.findOne({
      where: { grantCallSlug },
      select: ['id'],
    });

    if (!grantCall) {
      throw new NotFoundException(`Grant Call with slug '${grantCallSlug}' not found.`);
    }
    return grantCall.id;
  }

  async getApplicationStageSummaries(grantCallSlug: string): Promise<GrantCallApplicationStagesResponseDto> {
    const grantCall = await this.grantCallRepository.findOne({
      where: { grantCallSlug },
      select: ['id', 'grantCallSlug'],
    });

    if (!grantCall) {
      throw new NotFoundException(`Grant Call with slug "${grantCallSlug}" not found.`);
    }

    const stageSummaries = await this.workflowService.getStageSummariesWithCounts(
      WorkflowEntityType.APPLICATION,
      grantCall.id,
    );

    const totalApplications = await this.workflowService.countChildEntities(
      WorkflowEntityType.APPLICATION,
      grantCall.id,
    );

    return {
      grantCallSlug: grantCall.grantCallSlug,
      totalApplications,
      stages: stageSummaries,
    };
  }

  private buildDistributionRules(dto: GrantCallBaseDto): Partial<GrantDistributionRule>[] {
    return dto.grantDistribution.map((percentageValue, index) => ({
      rank: index + 1,
      type: DistributionType.PERCENTAGE,
      value: percentageValue,
    }));
  }

  private async getProgramAndStageDefinitions(
    grantProgramSlug: string,
    stageCodesToFetch: StageCode[],
    manager: EntityManager,
  ): Promise<{ grantProgram: GrantProgram; stepDefMap: Map<StageCode, WorkflowStepDefinition> }> {
    const grantProgramRepo = manager.getRepository(GrantProgram);
    const stepDefRepo = manager.getRepository(WorkflowStepDefinition);

    const grantProgram = await grantProgramRepo.findOneBy({ grantProgramSlug });

    if (!grantProgram) {
      throw new NotFoundException(`Grant Program '${grantProgramSlug}' not found`);
    }

    const stepDefs = await stepDefRepo.find({ where: { code: In(stageCodesToFetch) } });
    const stepDefMap = new Map(stepDefs.map((def) => [def.code, def]));

    return { grantProgram, stepDefMap };
  }

  private determineTimingProperties(stageCode: StageCode, dto: GrantCallBaseDto): GrantCallTimingProperties | null {
    if (!REQUIRED_TIMING_STAGES.includes(stageCode)) {
      return null;
    }

    const expectedTiming = STAGE_TIMING_EXPECTATION[stageCode];
    const timingResult: GrantCallTimingProperties = {};

    if (expectedTiming === StageTimingType.DATE_RANGE && stageCode === StageCode.GC_OPEN_FOR_APPLICATIONS) {
      timingResult.startDate = new Date(dto.openForApplicationStart);
      timingResult.endDate = new Date(dto.openForApplicationEnd);
    } else if (expectedTiming === StageTimingType.DURATION) {
      if (stageCode === StageCode.GC_COMMUNITY_VOTING) {
        timingResult.durationSeconds = dto.communityVotingTime1;
      } else if (stageCode === StageCode.GC_FINAL_COMMUNITY_VOTING) {
        timingResult.durationSeconds = dto.communityVotingTime2;
      }
    } else {
      this.logger.warn(`applyTimingSettings: Configuration mismatch for required timing stage ${stageCode}.`);
    }

    return timingResult;
  }

  private determineUrlProperty(
    stageCode: StageCode,
    dto: GrantCallBaseDto,
  ): Partial<Pick<GrantCallStageSetting, 'stageUrl'>> | null {
    const dtoKey = STAGE_URL_DTO_KEYS[stageCode];

    if (!dtoKey) {
      return null;
    }

    return { stageUrl: dto[dtoKey] ?? null };
  }

  private buildSingleStageSetting(
    stageCode: StageCode,
    dto: GrantCallBaseDto,
    stepDefMap: Map<StageCode, WorkflowStepDefinition>,
  ): GrantCallStageSetting | null {
    const stepDefinition = stepDefMap.get(stageCode);

    const timingProps = this.determineTimingProperties(stageCode, dto);
    const urlProps = this.determineUrlProperty(stageCode, dto);

    if (!timingProps && !urlProps) {
      return null;
    }

    const setting = new GrantCallStageSetting();
    setting.workflowStepDefinition = stepDefinition;

    Object.assign(setting, timingProps, urlProps);

    return setting;
  }

  private buildStageSettings(
    dto: GrantCallBaseDto,
    stepDefMap: Map<StageCode, WorkflowStepDefinition>,
  ): GrantCallStageSetting[] {
    return ALL_POSSIBLE_SETTING_STAGES.map((stageCode) =>
      this.buildSingleStageSetting(stageCode, dto, stepDefMap),
    ).filter((setting): setting is GrantCallStageSetting => setting !== null);
  }

  async getCallStageDefinitions(): Promise<StageDefinitionResponseDto[]> {
    return this.workflowService.getStageDefinitionsForEntityType(WorkflowEntityType.CALL);
  }

  async getCallStagesWithCounts(): Promise<StageWithCountResponseDto[]> {
    return this.workflowService.getStageSummariesWithCounts(WorkflowEntityType.CALL);
  }

  async updateGrantCallStatus(grantCallSlug: string, newStatus: WorkflowStatus): Promise<SingleTransitionResponseDto> {
    return this.grantCallRepository.manager.transaction(async (manager: EntityManager) => {
      const grantCall = await this.grantCallRepository.findOne({
        where: { grantCallSlug },
        relations: { workflowState: true },
      });

      if (!grantCall) {
        throw new NotFoundException(`Grant Call with slug "${grantCallSlug}" not found.`);
      }

      // Check if already in terminal status
      if (
        [WorkflowStatus.CLOSED, WorkflowStatus.REJECTED, WorkflowStatus.WITHDRAWN].includes(
          grantCall.workflowState.status,
        )
      ) {
        throw new UnprocessableEntityException(
          `Cannot change Grant Call status from terminal status '${grantCall.workflowState.status}'.`,
        );
      }

      // Update status to CLOSED
      await this.workflowTransitionService.updateStateStatus(WorkflowEntityType.CALL, grantCall.id, newStatus, manager);

      // Transition stage to GC_FINALIZED
      return await this.workflowTransitionService.setSpecificStage(
        WorkflowEntityType.CALL,
        grantCall.id,
        StageCode.GC_FINALIZED,
        manager,
      );
    });
  }
}
