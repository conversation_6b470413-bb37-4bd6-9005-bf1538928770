import { AuthModule } from '../auth/auth.module';
import { GrantApplication } from './entities/grant-application.entity';
import { GrantApplicationController } from './grant-application.controller';
import { GrantApplicationService } from './grant-application.service';
import { <PERSON><PERSON>all } from '../grant-call/entities/grant-call.entity';
import { GrantCallMapper } from '../grant-call/grant-call.mapper';
import { GrantProgram } from '../grant-program/entities/grant-program.entity';
import { HederaModule } from '../hedera/hedera.module';
import { MailModule } from '../notifications/mail/mail.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../auth/entities/user.entity';
import { WorkflowService } from '../workflow/workflow.service';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      GrantApplication,
      WorkflowTemplate,
      WorkflowStepDefinition,
      User,
      GrantCall,
      GrantProgram,
      WorkflowStepDefinition,
    ]),
    AuthModule,
    MailModule,
    HederaModule,
  ],
  controllers: [GrantApplicationController],
  providers: [GrantApplicationService, WorkflowService, WorkflowTransitionService, GrantCallMapper],
  exports: [GrantApplicationService],
})
export class GrantApplicationModule {}
