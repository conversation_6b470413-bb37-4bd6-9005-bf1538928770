import * as sanitizeDtoUtil from '../utils/sanitize.util';

import { CreateGrantApplicationDto, GetGrantApplicationsQueryDto } from './dto';
import { GrantApplicationStageCode, StageCode } from '../workflow/enums/stage-code.enum';
import { Test, TestingModule } from '@nestjs/testing';

import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { GrantApplication } from './entities/grant-application.entity';
import { GrantApplicationService } from './grant-application.service';
import { GrantCall } from '../grant-call/entities/grant-call.entity';
import { GrantCallMapper } from '../grant-call/grant-call.mapper';
import { GrantCategory } from '../grant-call/enums/grant-category.enum';
import { GrantProgram } from '../grant-program/entities/grant-program.entity';
import { HederaService } from '../hedera/hedera.service';
import { MailService } from '../notifications/mail/mail.service';
import { StageDefinitionResponseDto } from '../workflow/dto/stage-definition.response.dto';
import { StageTransitionType } from '../workflow/enums/stage-transition-type.enum';
import { TopicId } from '@hashgraph/sdk';
import { User } from '../auth/entities/user.entity';
import { WorkflowEntityType } from '../workflow/enums/workflow-entity-type.enum';
import { WorkflowService } from '../workflow/workflow.service';
import { WorkflowState } from '../workflow/entities/workflow-state.entity';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';
import { formatStageCode } from '../utils/formatting.util';
import { getRepositoryToken } from '@nestjs/typeorm';

jest.mock('../utils/formatting.util', () => ({
  formatStageCode: jest.fn((code) => {
    if (code === StageCode.GA_SCREENING) return 'Screening Stage';
    if (code === StageCode.GA_QUALIFICATION) return 'Qualification Stage';
    if (code === StageCode.GC_FINAL_COMMUNITY_VOTING) return 'Final Voting Stage';
    if (code) return `Formatted ${code}`;
    return null;
  }),
}));

const mockApplicationRepoTx = {
  create: jest.fn(),
  save: jest.fn(),
  findOne: jest.fn(),
  update: jest.fn(),
  count: jest.fn(),
};
const mockGrantCallRepoTx = { findOne: jest.fn() };
const mockStepDefRepoTx = { findOneBy: jest.fn() };
const mockWfTemplateRepoTx = { findOneBy: jest.fn() };
const mockWfStateRepoTx = { create: jest.fn(), save: jest.fn() };
const mockUserRepoTx = { findOne: jest.fn(), findOneBy: jest.fn() };

const transactionalRepositoryMap = new Map<any, any>([
  [GrantApplication, mockApplicationRepoTx],
  [GrantCall, mockGrantCallRepoTx],
  [WorkflowStepDefinition, mockStepDefRepoTx],
  [WorkflowTemplate, mockWfTemplateRepoTx],
  [WorkflowState, mockWfStateRepoTx],
  [User, mockUserRepoTx],
]);

const mockTransactionalEntityManager = {
  getRepository: jest.fn((entity: any): any => {
    const repo = transactionalRepositoryMap.get(entity);
    if (repo) return repo;
    throw new Error(`Mock getRepository not configured for ${typeof entity === 'function' ? entity.name : entity}`);
  }),
  create: jest.fn((entityType, plainObject) => Object.assign(new entityType(), plainObject)),
  save: jest.fn((entity) => Promise.resolve(entity)),
};

const mockApplicationRepositoryValue = {
  findOne: jest.fn(),
  find: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    innerJoin: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    getMany: jest.fn().mockResolvedValue([]),
    getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
    getOne: jest.fn().mockResolvedValue(null),
    setParameters: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
  })),
  countBy: jest.fn(),
  update: jest.fn(),
  manager: {
    transaction: jest.fn().mockImplementation(async (param1: any, param2?: any) => {
      const actualCallback = typeof param1 === 'function' ? param1 : param2;
      return actualCallback(mockTransactionalEntityManager);
    }),
  },
};

const mockAuthRepositoryValue = { findOne: jest.fn() };

const mockGrantCallMapperValue = { mapGrantCallToDetailDto: jest.fn(), mapGrantCallToBaseDto: jest.fn() };
const mockMailServiceValue = {
  sendApplicationMovedNotification: jest.fn(),
  sendApplicationApprovedNotification: jest.fn(),
  sendApplicationRejectedNotification: jest.fn(),
};
const mockHederaServiceValue = {
  createTopicWithRetry: jest.fn(),
  submitMessage: jest.fn(),
  getMessagesFromTopic: jest.fn(),
};
const mockWorkflowServiceValue = {
  initializeEntityState: jest.fn(),
  getStageDefinitionsForEntityType: jest.fn(),
  getStageSummariesWithCounts: jest.fn(),
  countChildEntities: jest.fn(),
  stageDefinitionToDto: jest.fn(),
};
const mockWorkflowTransitionServiceValue = {
  transitionSingleStateToNextStep: jest.fn(),
  updateStateStatus: jest.fn(),
};

const mockDataSourceValue = {
  createQueryRunner: jest.fn(() => ({})),
  manager: {
    transaction: jest.fn().mockImplementation(async (param1: any, param2?: any) => {
      const actualCallback = typeof param1 === 'function' ? param1 : param2;
      return actualCallback(mockTransactionalEntityManager);
    }),
  },
};

jest.mock('../utils/sanitize.util.ts', () => ({
  sanitizeDto: jest.fn((dto) => dto),
}));

describe('GrantApplicationService', () => {
  let service: GrantApplicationService;
  let applicationRepo: typeof mockApplicationRepositoryValue;
  let hederaService: typeof mockHederaServiceValue;
  let workflowService: typeof mockWorkflowServiceValue;

  const mockGrantCallOpen = {
    id: 1,
    name: 'Open Call',
    grantCallSlug: 'open-call',
    workflowState: { currentStepDefinition: { code: StageCode.GC_OPEN_FOR_APPLICATIONS } },
  } as GrantCall;

  const mockWorkflowTemplateApp = { id: 3, entityType: WorkflowEntityType.APPLICATION } as WorkflowTemplate;
  const mockStepDefScreening = { id: 101, code: StageCode.GA_SCREENING, name: 'Screening' } as WorkflowStepDefinition;
  const mockUserCreator = { id: 1, displayName: 'Applicant User', email: '<EMAIL>' } as User;

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GrantApplicationService,
        ConfigService,
        { provide: getRepositoryToken(GrantApplication), useValue: mockApplicationRepositoryValue },
        { provide: getRepositoryToken(User), useValue: mockAuthRepositoryValue },
        {
          provide: getRepositoryToken(GrantCall),
          useValue: { findOne: jest.fn() },
        },
        { provide: GrantCallMapper, useValue: mockGrantCallMapperValue },

        { provide: MailService, useValue: mockMailServiceValue },
        { provide: HederaService, useValue: mockHederaServiceValue },
        { provide: WorkflowService, useValue: mockWorkflowServiceValue },
        { provide: WorkflowTransitionService, useValue: mockWorkflowTransitionServiceValue },
        { provide: DataSource, useValue: mockDataSourceValue },
      ],
    }).compile();

    service = module.get<GrantApplicationService>(GrantApplicationService);
    applicationRepo = module.get(getRepositoryToken(GrantApplication));
    hederaService = module.get(HederaService);
    workflowService = module.get(WorkflowService);

    (sanitizeDtoUtil.sanitizeDto as jest.Mock).mockImplementation((dto) => dto);
  });

  describe('create', () => {
    let createDto: CreateGrantApplicationDto;
    const requestingUserId = 1;
    const topicIdFromString = TopicId.fromString('0.0.123');

    beforeEach(() => {
      createDto = {
        grantCallSlug: 'open-call',
        title: 'Test App',
        description: 'Test Desc',
        companyName: 'Test Co',
        companyCountry: 'Testland',
        contactFullName: 'Test Contact',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '123',
        categories: [GrantCategory.SUSTAINABILITY],
      };

      mockGrantCallRepoTx.findOne.mockResolvedValue(mockGrantCallOpen);
      mockWfTemplateRepoTx.findOneBy.mockResolvedValue(mockWorkflowTemplateApp);
      mockStepDefRepoTx.findOneBy.mockResolvedValue(mockStepDefScreening);
      mockWfStateRepoTx.create.mockImplementation((data) => ({ ...data, id: 201 }));
      mockWfStateRepoTx.save.mockImplementation((state) => Promise.resolve({ ...state, id: state.id ?? 201 }));
      mockHederaServiceValue.createTopicWithRetry.mockResolvedValue(topicIdFromString);
      mockApplicationRepoTx.create.mockImplementation(
        (data: Partial<GrantApplication>) =>
          ({
            id: 0,
            ...data,
            grantCall: data.grantCall,
            workflowState: {
              ...data.workflowState,
              currentStepDefinition: data.workflowState?.currentStepDefinition || mockStepDefScreening,
            } as WorkflowState,
            actionTopicId: topicIdFromString.toString(),
          }) as GrantApplication,
      );
      mockApplicationRepoTx.save.mockImplementation(async (app) => ({
        ...app,
        id: app.id === 0 ? 1001 : app.id,
      }));
      mockAuthRepositoryValue.findOne.mockResolvedValue(mockUserCreator);

      mockApplicationRepositoryValue.manager.transaction.mockClear();
    });

    it('should successfully create a grant application and anchor event', async () => {
      const result = await service.create(createDto, requestingUserId);

      expect(result).toEqual({ grantApplicationId: 1001 });
      expect(applicationRepo.manager.transaction).toHaveBeenCalledTimes(1);

      expect(mockGrantCallRepoTx.findOne).toHaveBeenCalledWith({
        where: { grantCallSlug: createDto.grantCallSlug },
        relations: { workflowState: { currentStepDefinition: true } },
      });
      expect(mockWfTemplateRepoTx.findOneBy).toHaveBeenCalledWith({ entityType: WorkflowEntityType.APPLICATION });
      expect(mockStepDefRepoTx.findOneBy).toHaveBeenCalledWith({ code: StageCode.GA_SCREENING });
      expect(mockWfStateRepoTx.save).toHaveBeenCalled();
      expect(mockHederaServiceValue.createTopicWithRetry).toHaveBeenCalledTimes(1);
      expect(mockApplicationRepoTx.save).toHaveBeenCalledTimes(1);

      const savedAppArg = mockApplicationRepoTx.save.mock.calls[0][0];
      expect(savedAppArg.title).toEqual(createDto.title);

      expect(savedAppArg.grantCall.id).toEqual(mockGrantCallOpen.id);
      expect(savedAppArg.workflowState.currentStepDefinition.id).toEqual(mockStepDefScreening.id);
      expect(savedAppArg.actionTopicId).toEqual(topicIdFromString.toString());
      expect(mockAuthRepositoryValue.findOne).toHaveBeenCalledWith({ where: { id: requestingUserId } });
      expect(mockHederaServiceValue.submitMessage).toHaveBeenCalledTimes(1);
      expect(mockHederaServiceValue.submitMessage).toHaveBeenCalledWith(
        '0.0.123',
        expect.stringContaining('"reason":"Application Created and Submitted"'),
      );
      expect(mockHederaServiceValue.submitMessage).toHaveBeenCalledWith(
        '0.0.123',
        expect.stringContaining(`"current_status":"${formatStageCode(StageCode.GA_SCREENING)}"`),
      );
    });
  });

  describe('findGrantApplication', () => {
    const testApplicationId = 1;
    let mockFullApplicationEntity: GrantApplication;

    beforeEach(() => {
      const mockUserEntity = { id: 101, displayName: 'App Creator', email: '<EMAIL>' } as User;
      const mockGrantCallEntity = {
        id: 201,
        grantCallSlug: 'test-gc-for-app',
        name: 'Test GC',
        createdBy: { id: 501, displayName: 'GC Creator', email: '<EMAIL>' } as User,
        createdById: 501,
        stageSettings: [],
        distributionRules: [],
        workflowState: {
          currentStepDefinition: { code: StageCode.GC_OPEN_FOR_APPLICATIONS, id: 701 } as WorkflowStepDefinition,
        } as WorkflowState,
        grantProgram: {
          id: 301,
          grantProgramSlug: 'test-gp-slug',
          name: 'Associated Program',
          grantProgramCoordinator: { id: 401, displayName: 'Prog Coord', email: '<EMAIL>' } as User,
        } as GrantProgram,
      } as GrantCall;

      mockFullApplicationEntity = {
        id: testApplicationId,
        title: 'Full Detailed Application',
        description: 'Full Description',
        categories: [GrantCategory.CONSUMER_LOYALTY],
        companyName: 'Tech Solutions Inc.',
        companyCountry: 'Innovation Land',
        companyWebpage: 'http://techsolutions.inc',
        contactFullName: 'Techy McTester',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '555-TECH',
        actionTopicId: 'action-topic-789',
        votingTopicId: 'vote-topic-101',
        createdAt: new Date('2024-02-15T10:00:00Z'),
        updatedAt: new Date('2024-02-16T14:30:00Z'),
        createdById: mockUserEntity.id,
        createdBy: mockUserEntity,
        grantCall: mockGrantCallEntity,
        workflowState: {
          id: 901,
          status: WorkflowStatus.IN_PROGRESS,
          currentStepDefinitionId: 955,
          currentStepDefinition: {
            id: 955,
            code: StageCode.GA_SCREENING,
            name: 'Under Review',
          } as WorkflowStepDefinition,
        } as WorkflowState,
      } as unknown as GrantApplication;

      applicationRepo.findOne.mockResolvedValue(mockFullApplicationEntity);
    });

    it('should successfully find and map the grant application', async () => {
      const result = await service.findGrantApplication(testApplicationId);

      expect(applicationRepo.findOne).toHaveBeenCalledTimes(1);
      expect(applicationRepo.findOne).toHaveBeenCalledWith({
        where: { id: testApplicationId },
        relations: {
          createdBy: true,
          workflowState: {
            currentStepDefinition: true,
          },
          grantCall: {
            createdBy: true,
            stageSettings: { workflowStepDefinition: true },
            distributionRules: true,
            grantProgram: { grantProgramCoordinator: true },
            workflowState: { currentStepDefinition: true, workflowTemplate: { steps: true } },
          },
        },
      });

      expect(result).toBeDefined();
      expect(result.id).toEqual(testApplicationId);
      expect(result.title).toEqual('Full Detailed Application');
      expect(result.creator.id).toEqual(mockFullApplicationEntity.createdById);
      expect(result.creator.displayName).toEqual(mockFullApplicationEntity.createdBy.displayName);
      expect(result.status).toEqual(WorkflowStatus.IN_PROGRESS);
      expect(result.currentStage).toEqual(
        workflowService.stageDefinitionToDto(mockFullApplicationEntity.workflowState.currentStepDefinition),
      );
      expect(result.grantCallSlug).toEqual(mockFullApplicationEntity.grantCall.grantCallSlug);
      expect(result.createdAt).toEqual('2024-02-15T10:00:00.000Z');
      expect(result.description).toEqual('Full Description');
      expect(result.categories).toEqual([GrantCategory.CONSUMER_LOYALTY]);
      expect(result.companyName).toEqual('Tech Solutions Inc.');
      expect(result.contactEmail).toEqual('<EMAIL>');
    });
  });

  describe('getApplicationActivityLog', () => {
    const applicationId = 123;
    const mockActionTopicId = '0.0.777';

    const mockStageDefs: StageDefinitionResponseDto[] = [
      {
        id: 1,
        code: StageCode.GA_SCREENING,
        name: 'Screening Stage',
        position: 10,
        workflowTemplateId: 0,
        transitionType: StageTransitionType.MANUAL,
        isTerminal: false,
      },
      {
        id: 2,
        code: StageCode.GA_SCREENING,
        name: 'Review Stage',
        position: 20,
        workflowTemplateId: 0,
        transitionType: StageTransitionType.MANUAL,
        isTerminal: false,
      },
      {
        id: 3,
        code: StageCode.GA_FINAL_QUALIFICATION,
        name: 'Final Voting Stage',
        position: 30,
        workflowTemplateId: 0,
        transitionType: StageTransitionType.MANUAL,
        isTerminal: false,
      },
    ];

    const mockApplicationWithTopic = { id: applicationId, actionTopicId: mockActionTopicId };
    const mockApplicationWithoutTopic = { id: applicationId, actionTopicId: null };

    beforeEach(() => {
      workflowService.getStageDefinitionsForEntityType.mockResolvedValue(mockStageDefs);
      applicationRepo.findOne.mockResolvedValue(mockApplicationWithTopic);
      hederaService.getMessagesFromTopic.mockResolvedValue([]);
    });

    it('should return structured activity log for an application with messages', async () => {
      const hederaMessages = [
        {
          consensus_timestamp: 1748250979.783347,
          message: JSON.stringify({
            previous_status: '',
            current_status: 'Screening Stage',
            who: 'User A',
            reason: 'Application Submitted',
          }),
        },
        {
          consensus_timestamp: 1748250980.123456,
          message: JSON.stringify({
            previous_status: 'Screening Stage',
            current_status: 'Review Stage',
            who: 'User B',
            reason: 'Moved to Review',
          }),
        },
      ];
      hederaService.getMessagesFromTopic.mockResolvedValue(hederaMessages);

      const result = await service.getApplicationActivityLog(applicationId);

      expect(result.applicationId).toEqual(applicationId);
      expect(workflowService.getStageDefinitionsForEntityType).toHaveBeenCalledWith(WorkflowEntityType.APPLICATION);
      expect(applicationRepo.findOne).toHaveBeenCalledWith({
        where: { id: applicationId },
        select: ['id', 'actionTopicId'],
      });
      expect(hederaService.getMessagesFromTopic).toHaveBeenCalledWith(mockActionTopicId);

      expect(result.activityLog).toHaveLength(1);

      const screeningStageGroup = result.activityLog[0];
      expect(screeningStageGroup).toBeDefined();
      expect(screeningStageGroup.stageName).toEqual('Screening Stage');
      expect(screeningStageGroup.position).toEqual(10);
      expect(screeningStageGroup.activities).toHaveLength(2);
      expect(screeningStageGroup.activities[0]).toEqual(
        expect.objectContaining({
          fromStage: '',
          toStage: 'Screening Stage',
          who: 'User A',
          reason: 'Application Submitted',
          timestamp: '2025-05-26T09:16:19.783Z',
        }),
      );
      expect(screeningStageGroup.activities[1]).toEqual(
        expect.objectContaining({
          fromStage: 'Screening Stage',
          toStage: 'Review Stage',
          who: 'User B',
          reason: 'Moved to Review',
          timestamp: '2025-05-26T09:16:20.123Z',
        }),
      );

      const reviewStageGroup = result.activityLog.find((g) => g.stageName === 'Review Stage');
      expect(reviewStageGroup).toBeUndefined();
    });

    it('should handle messages correctly including sorting by timestamp (if present, otherwise order of messages)', async () => {
      const hederaMessages = [
        {
          consensus_timestamp: 1748250979.783347,
          message: JSON.stringify({
            previous_status: 'Screening Stage',
            current_status: 'Review Stage',
            who: 'User B',
            reason: 'Later Action on Screening',
          }),
        },
        {
          consensus_timestamp: 1748250980.123456,
          message: JSON.stringify({
            previous_status: 'Screening Stage',
            current_status: 'Review Stage',
            who: 'User A',
            reason: 'Earlier Action on Screening',
          }),
        },
      ];
      hederaService.getMessagesFromTopic.mockResolvedValue(hederaMessages);
      workflowService.getStageDefinitionsForEntityType.mockResolvedValue([mockStageDefs[0]]);

      const result = await service.getApplicationActivityLog(applicationId);
      const screeningStageLog = result.activityLog.find((log) => log.stageCode === GrantApplicationStageCode.SCREENING);
      expect(screeningStageLog?.activities[0].reason).toBe('Later Action on Screening');
      expect(screeningStageLog?.activities[1].reason).toBe('Earlier Action on Screening');
    });

    it('should return stage structure with empty activities if application has no actionTopicId', async () => {
      applicationRepo.findOne.mockResolvedValue(mockApplicationWithoutTopic);

      const result = await service.getApplicationActivityLog(applicationId);

      expect(hederaService.getMessagesFromTopic).not.toHaveBeenCalled();
      expect(result.applicationId).toEqual(applicationId);
      expect(result.activityLog).toHaveLength(0);
    });

    it('should return an empty activityLog if Hedera topic messages are empty', async () => {
      applicationRepo.findOne.mockResolvedValue(mockApplicationWithTopic);
      hederaService.getMessagesFromTopic.mockResolvedValue([]);

      const result = await service.getApplicationActivityLog(applicationId);

      expect(result.applicationId).toEqual(applicationId);
      expect(result.activityLog).toHaveLength(0);
    });
  });

  describe('findGrantApplications', () => {
    let mockQueryBuilder;
    let mockApplications: GrantApplication[];

    const mockUserApplicant = { id: 1, displayName: 'Applicant', email: '<EMAIL>' } as User;
    const mockUserOther = { id: 2, displayName: 'Other User', email: '<EMAIL>' } as User;

    const mockGrantCall1 = {
      id: 10,
      grantCallSlug: 'gc-slug-1',
      name: 'Grant Call 1',
      grantProgram: { grantProgramSlug: 'gp-slug-1' },
    } as GrantCall;
    const mockGrantCall2 = {
      id: 11,
      grantCallSlug: 'gc-slug-2',
      name: 'Grant Call 2',
      grantProgram: { grantProgramSlug: 'gp-slug-2' },
    } as GrantCall;

    const mockStepDefScreening = { id: 101, code: StageCode.GA_SCREENING, name: 'Screening' } as WorkflowStepDefinition;
    const mockStepDefQualification = {
      id: 102,
      code: StageCode.GA_QUALIFICATION,
      name: 'Qualification',
    } as WorkflowStepDefinition;

    const baseMockApplication: Partial<GrantApplication> = {
      id: 0,
      title: 'Test App',
      description: 'Desc',
      categories: [GrantCategory.SUSTAINABILITY],
      companyName: 'TestCo',
      companyCountry: 'Testland',
      actionTopicId: 'topic1',
      votingTopicId: 'vote1',
      createdAt: new Date('2024-01-01T00:00:00.000Z'),
      updatedAt: new Date('2024-01-01T00:00:00.000Z'),
      contactFullName: 'Test Contact',
      contactEmail: '<EMAIL>',
      contactPhoneNumber: '123',
      workflowState: {
        status: WorkflowStatus.IN_PROGRESS,
        currentStepDefinitionId: mockStepDefScreening.id,
        currentStepDefinition: mockStepDefScreening,
      } as WorkflowState,
      createdById: 0,
    };

    beforeEach(() => {
      mockApplications = [
        {
          ...baseMockApplication,
          id: 1,
          createdById: mockUserApplicant.id,
          createdBy: mockUserApplicant,
          grantCall: mockGrantCall1,
          workflowState: {
            ...baseMockApplication.workflowState,
            currentStepDefinition: mockStepDefScreening,
            currentStepDefinitionId: mockStepDefScreening.id,
          },
          title: 'My App 1 (Screening)',
          createdAt: new Date('2024-01-02T00:00:00.000Z'),
          updatedAt: new Date('2024-01-03T00:00:00.000Z'),
        } as GrantApplication,
        {
          ...baseMockApplication,
          id: 2,
          createdById: mockUserApplicant.id,
          createdBy: mockUserApplicant,
          grantCall: mockGrantCall2,
          workflowState: {
            ...baseMockApplication.workflowState,
            currentStepDefinition: mockStepDefQualification,
            currentStepDefinitionId: mockStepDefQualification.id,
          },
          title: 'My App 2 (Qualification)',
          createdAt: new Date('2024-01-03T00:00:00.000Z'),
          updatedAt: new Date('2024-01-03T00:00:00.000Z'),
        } as GrantApplication,
        {
          ...baseMockApplication,
          id: 3,
          createdById: mockUserOther.id,
          createdBy: mockUserOther,
          grantCall: mockGrantCall1,
          workflowState: {
            ...baseMockApplication.workflowState,
            currentStepDefinition: mockStepDefScreening,
            currentStepDefinitionId: mockStepDefScreening.id,
          },
          title: 'Other User App (Screening)',
          createdAt: new Date('2024-01-01T00:00:00.000Z'),
          updatedAt: new Date('2024-01-03T00:00:00.000Z'),
        } as GrantApplication,
      ];

      mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockApplications),
      };
      applicationRepo.createQueryBuilder = jest.fn(() => mockQueryBuilder);

      jest.spyOn(service as any, 'mapApplicationToDetailDto');
    });

    it('should return all applications when no query parameters are provided', async () => {
      const query: GetGrantApplicationsQueryDto = {};
      mockQueryBuilder.getMany.mockResolvedValue(mockApplications);

      const result = await service.findGrantApplications(query, null);

      expect(applicationRepo.createQueryBuilder).toHaveBeenCalledWith('application');
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith('application.createdBy', 'creatorUser');
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith('application.grantCall', 'grantCall');
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(expect.stringContaining('grantCallSlug'));
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(expect.stringContaining('createdById'));
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(expect.stringContaining('appCurrentStepDef.code IN'));
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('application.createdAt', 'DESC');
      expect(mockQueryBuilder.getMany).toHaveBeenCalledTimes(1);
      expect(result).toHaveLength(mockApplications.length);
      expect((service as any).mapApplicationToDetailDto).toHaveBeenCalledTimes(mockApplications.length);
      expect(result[0].title).toBe('My App 1 (Screening)');
    });

    it('should filter by grantCallSlug', async () => {
      const query: GetGrantApplicationsQueryDto = { grantCallSlug: 'gc-slug-1' };
      const filteredApps = mockApplications.filter((app) => app.grantCall.grantCallSlug === 'gc-slug-1');
      mockQueryBuilder.getMany.mockResolvedValue(filteredApps);

      const result = await service.findGrantApplications(query, null);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('grantCall.grantCallSlug = :grantCallSlug', {
        grantCallSlug: 'gc-slug-1',
      });
      expect(result).toHaveLength(filteredApps.length);
      result.forEach((appDto) => expect(appDto.grantCallSlug).toEqual('gc-slug-1'));
    });

    it('should filter by myApplications for a requesting user', async () => {
      const requestingUserId = mockUserApplicant.id;
      const query: GetGrantApplicationsQueryDto = { myApplications: true };
      const filteredApps = mockApplications.filter((app) => app.createdById === requestingUserId);
      mockQueryBuilder.getMany.mockResolvedValue(filteredApps);

      const result = await service.findGrantApplications(query, requestingUserId);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('application.createdById = :requestingUserId', {
        requestingUserId,
      });
      expect(result).toHaveLength(filteredApps.length);
      result.forEach((appDto) => expect(appDto.creator.id).toEqual(requestingUserId));
    });

    it('should filter by a single stageCode', async () => {
      const query: GetGrantApplicationsQueryDto = { stageCodes: [StageCode.GA_QUALIFICATION] };
      const filteredApps = mockApplications.filter(
        (app) => app.workflowState.currentStepDefinition.code === StageCode.GA_QUALIFICATION,
      );
      mockQueryBuilder.getMany.mockResolvedValue(filteredApps);

      const result = await service.findGrantApplications(query, null);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('appCurrentStepDef.code IN (:...stageCodesToFilter)', {
        stageCodesToFilter: [StageCode.GA_QUALIFICATION],
      });
      expect(result).toHaveLength(filteredApps.length);
      result.forEach((appDto) =>
        expect(appDto.currentStage).toEqual(workflowService.stageDefinitionToDto(mockStepDefQualification)),
      );
    });

    it('should filter by multiple stageCodes', async () => {
      const stagesToFilter = [StageCode.GA_SCREENING, StageCode.GA_QUALIFICATION];
      const query: GetGrantApplicationsQueryDto = { stageCodes: stagesToFilter };
      const filteredApps = mockApplications.filter((app) =>
        stagesToFilter.includes(app.workflowState.currentStepDefinition.code),
      );
      mockQueryBuilder.getMany.mockResolvedValue(filteredApps);

      const result = await service.findGrantApplications(query, null);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('appCurrentStepDef.code IN (:...stageCodesToFilter)', {
        stageCodesToFilter: stagesToFilter,
      });
      expect(result).toHaveLength(filteredApps.length);
    });

    it('should apply all filters together (slug, myApplications, stage)', async () => {
      const requestingUserId = mockUserApplicant.id;
      const query: GetGrantApplicationsQueryDto = {
        grantCallSlug: 'gc-slug-1',
        myApplications: true,
        stageCodes: [StageCode.GA_SCREENING],
      };
      const filteredApps = mockApplications.filter(
        (app) =>
          app.grantCall.grantCallSlug === 'gc-slug-1' &&
          app.createdById === requestingUserId &&
          app.workflowState.currentStepDefinition.code === StageCode.GA_SCREENING,
      );
      mockQueryBuilder.getMany.mockResolvedValue(filteredApps);

      const result = await service.findGrantApplications(query, requestingUserId);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('grantCall.grantCallSlug = :grantCallSlug', {
        grantCallSlug: 'gc-slug-1',
      });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('application.createdById = :requestingUserId', {
        requestingUserId,
      });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('appCurrentStepDef.code IN (:...stageCodesToFilter)', {
        stageCodesToFilter: [StageCode.GA_SCREENING],
      });
      expect(result).toHaveLength(filteredApps.length);
      expect(result[0].title).toBe('My App 1 (Screening)');
    });
  });
});
