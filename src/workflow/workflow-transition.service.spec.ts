import { <PERSON>Source, QueryRunner, Repository, UpdateResult } from 'typeorm';
import { Logger, UnprocessableEntityException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { StageCode } from './enums/stage-code.enum';
import { StageTransitionType } from './enums/stage-transition-type.enum';
import { WorkflowEntityType } from './enums/workflow-entity-type.enum';
import { WorkflowState } from './entities/workflow-state.entity';
import { WorkflowStatus } from './enums/workflow-status.enum';
import { WorkflowStepDefinition } from './entities/workflow-step-definition.entity';
import { WorkflowTemplate } from './entities/workflow-template.entity';
import { WorkflowTransitionService } from './workflow-transition.service';
import { getRepositoryToken } from '@nestjs/typeorm';

const createMockRepository = () => ({
  findOne: jest.fn(),
  findOneBy: jest.fn(),
  find: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  createQueryBuilder: jest.fn(),
});

const createMockQueryBuilder = () => ({
  innerJoin: jest.fn().mockReturnThis(),
  leftJoinAndSelect: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  andWhere: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  getOne: jest.fn(),
  getMany: jest.fn(),
  update: jest.fn().mockReturnThis(),
  set: jest.fn().mockReturnThis(),
  whereInIds: jest.fn().mockReturnThis(),
  execute: jest.fn(),
  expressionMap: {
    relationMetadatas: [],
    mainAlias: { name: 'mockAlias' },
    aliases: [],
    joinAttributes: [],
    selects: [],
  },
});

const createMockEntityManager = () => ({
  findOne: jest.fn(),
  findOneBy: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  createQueryBuilder: jest.fn(),
  transaction: jest.fn().mockImplementation(async (cb) => {
    const transactionalManager = createMockEntityManager();
    transactionalManager.getRepository = jest.fn().mockImplementation((entity) => {
      if (entity === WorkflowState) {
        const mockWfRepo = createMockRepository();
        mockWfRepo.createQueryBuilder = jest.fn(() => createMockQueryBuilder());
        return mockWfRepo;
      }
      return createMockRepository();
    });
    transactionalManager.createQueryBuilder = jest.fn(() => createMockQueryBuilder());
    return cb(transactionalManager);
  }),
  getRepository: jest.fn().mockImplementation((entity) => {
    if (entity === WorkflowState) {
      const repo = createMockRepository();
      repo.createQueryBuilder = jest.fn(() => createMockQueryBuilder());
      return repo;
    }
    return createMockRepository();
  }),
});

const createMockDataSource = () => ({
  manager: createMockEntityManager(),
  createQueryRunner: jest.fn().mockReturnValue({
    connect: jest.fn().mockResolvedValue(undefined),
    startTransaction: jest.fn().mockResolvedValue(undefined),
    commitTransaction: jest.fn().mockResolvedValue(undefined),
    rollbackTransaction: jest.fn().mockResolvedValue(undefined),
    release: jest.fn().mockResolvedValue(undefined),
    manager: createMockEntityManager(),
  } as unknown as QueryRunner),
});

describe('WorkflowTransitionService', () => {
  let service: WorkflowTransitionService;
  let stepDefinitionRepository: Repository<WorkflowStepDefinition>;
  let dataSource;
  let entityManager;

  const mockTemplate1 = { id: 1 } as WorkflowTemplate;
  const mockTemplate2 = { id: 2 } as WorkflowTemplate;

  const mockGcStep1: WorkflowStepDefinition = {
    id: 1,
    workflowTemplateId: mockTemplate1.id,
    workflowTemplate: mockTemplate1,
    name: 'Open for Applications',
    code: StageCode.GC_OPEN_FOR_APPLICATIONS,
    sequenceNumber: 1,
    transitionType: StageTransitionType.MANUAL,
    description: 'Grant Call is accepting applications',
    isTerminal: false,
  };

  const mockGcStep2: WorkflowStepDefinition = {
    id: 2,
    workflowTemplateId: mockTemplate1.id,
    workflowTemplate: mockTemplate1,
    name: 'Screening',
    code: StageCode.GC_SCREENING,
    sequenceNumber: 2,
    transitionType: StageTransitionType.MANUAL,
    description: 'Screening applications for the call',
    isTerminal: false,
  };

  const mockGcStepTerminal: WorkflowStepDefinition = {
    id: 3,
    workflowTemplateId: mockTemplate1.id,
    workflowTemplate: mockTemplate1,
    name: 'Closed',
    code: StageCode.GC_CLOSED,
    sequenceNumber: 3,
    transitionType: StageTransitionType.MANUAL,
    description: 'Grant Call is closed',
    isTerminal: true,
  };

  const mockGaStep1: WorkflowStepDefinition = {
    id: 10,
    workflowTemplateId: mockTemplate2.id,
    workflowTemplate: mockTemplate2,
    name: 'Application Screening',
    code: StageCode.GA_SCREENING,
    sequenceNumber: 1,
    transitionType: StageTransitionType.MANUAL,
    description: 'Initial screening of application',
    isTerminal: false,
  };

  const mockGaStep2: WorkflowStepDefinition = {
    id: 11,
    workflowTemplateId: mockTemplate2.id,
    workflowTemplate: mockTemplate2,
    name: 'Qualification',
    code: StageCode.GA_QUALIFICATION,
    sequenceNumber: 2,
    transitionType: StageTransitionType.MANUAL,
    description: 'Qualification checks',
    isTerminal: false,
  };

  const mockGaStepFinalQual: WorkflowStepDefinition = {
    id: 15,
    workflowTemplateId: mockTemplate2.id,
    workflowTemplate: mockTemplate2,
    name: 'Final Qualification',
    code: StageCode.GA_FINAL_QUALIFICATION,
    sequenceNumber: 5,
    transitionType: StageTransitionType.MANUAL,
    description: 'Final step before approval/rejection',
    isTerminal: false,
  };

  const mockCallState: WorkflowState = {
    id: 101,
    workflowTemplateId: mockTemplate1.id,
    workflowTemplate: null,
    currentStepDefinitionId: mockGcStep1.id,
    currentStepDefinition: mockGcStep1,
    status: WorkflowStatus.IN_PROGRESS,
    currentStepTransitionedAt: new Date('2023-10-01T00:00:00Z'),
    currentStepEndsAt: null,
  };

  const mockAppState: WorkflowState = {
    id: 201,
    workflowTemplateId: mockTemplate2.id,
    workflowTemplate: null,
    currentStepDefinitionId: mockGaStep1.id,
    currentStepDefinition: mockGaStep1,
    status: WorkflowStatus.READY_FOR_NEXT_STEP,
    currentStepTransitionedAt: new Date('2023-10-02T00:00:00Z'),
    currentStepEndsAt: new Date('2023-10-10T00:00:00Z'),
  };

  beforeEach(async () => {
    entityManager = createMockEntityManager();
    dataSource = createMockDataSource();
    dataSource.manager = entityManager;

    dataSource.manager.transaction = jest.fn().mockImplementation(async (cb) => {
      const transactionalManager = createMockEntityManager();
      return cb(transactionalManager);
    });

    dataSource.manager.getRepository = jest.fn().mockImplementation((entity) => {
      if (entity === WorkflowState) {
        const repo = createMockRepository();
        repo.createQueryBuilder = jest.fn(() => createMockQueryBuilder());
        return repo;
      }
      return createMockRepository();
    });

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkflowTransitionService,
        { provide: getRepositoryToken(WorkflowStepDefinition), useValue: createMockRepository() },
        { provide: DataSource, useValue: dataSource },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
            setContext: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<WorkflowTransitionService>(WorkflowTransitionService);
    stepDefinitionRepository = module.get(getRepositoryToken(WorkflowStepDefinition));

    jest.spyOn(service, 'findWorkflowStateByEntity');
    jest.spyOn(service as any, 'getNextStepDefinition').mockResolvedValue(mockGcStep2);
    jest.spyOn(service as any, 'calculateStepEndDate').mockResolvedValue(null);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('transitionSingleStateToNextStep', () => {
    const entityId = 1;
    const mockNow = new Date('2023-10-27T10:00:00Z');

    beforeEach(() => {
      jest.useFakeTimers().setSystemTime(mockNow);
      jest.spyOn(service, 'findWorkflowStateByEntity').mockResolvedValue({ ...mockCallState });
      jest.spyOn(service as any, 'getNextStepDefinition').mockResolvedValue(mockGcStep2);
      entityManager.save = jest.fn().mockResolvedValue({ ...mockCallState, currentStepDefinitionId: mockGcStep2.id });
    });

    it('should transition a Grant Call state and calculate end date', async () => {
      const entityType = WorkflowEntityType.CALL;
      const calculatedEndDate = new Date('2023-11-10T10:00:00Z');
      const calculateStepEndDateSpy = jest
        .spyOn(service as any, 'calculateStepEndDate')
        .mockResolvedValue(calculatedEndDate);

      const result = await service.transitionSingleStateToNextStep(entityType, entityId);

      expect(service.findWorkflowStateByEntity).toHaveBeenCalledWith(entityType, entityId, undefined);
      expect((service as any).getNextStepDefinition).toHaveBeenCalledWith(mockCallState.currentStepDefinition);
      expect(calculateStepEndDateSpy).toHaveBeenCalledWith(mockNow, mockGcStep2, entityId, dataSource.manager);
      expect(entityManager.save).toHaveBeenCalledWith(
        WorkflowState,
        expect.objectContaining({
          id: mockCallState.id,
          currentStepDefinition: mockGcStep2,
          status: WorkflowStatus.IN_PROGRESS,
          currentStepTransitionedAt: mockNow,
          currentStepEndsAt: calculatedEndDate,
        }),
      );
      expect(result).toEqual({
        stepDefinitionCode: mockGcStep2.code,
        status: WorkflowStatus.IN_PROGRESS,
      });
    });

    it('should transition a Grant Application state', async () => {
      const entityType = WorkflowEntityType.APPLICATION;
      const appEntityId = 5;
      const mockAppSpecificState = {
        ...mockAppState,
        id: 301,
        currentStepDefinitionId: mockGaStep1.id,
        currentStepDefinition: mockGaStep1,
      };
      jest.spyOn(service, 'findWorkflowStateByEntity').mockResolvedValue(mockAppSpecificState);
      jest.spyOn(service as any, 'getNextStepDefinition').mockResolvedValue(mockGaStep2);
      entityManager.save.mockResolvedValue({
        ...mockAppSpecificState,
        currentStepDefinition: mockGaStep2,
        currentStepDefinitionId: mockGaStep2.id,
      });

      const result = await service.transitionSingleStateToNextStep(entityType, appEntityId);

      expect(service.findWorkflowStateByEntity).toHaveBeenCalledWith(entityType, appEntityId, undefined);
      expect((service as any).getNextStepDefinition).toHaveBeenCalledWith(mockGaStep1);
      expect((service as any).calculateStepEndDate).not.toHaveBeenCalled();
      expect(entityManager.save).toHaveBeenCalledWith(
        WorkflowState,
        expect.objectContaining({
          id: mockAppSpecificState.id,
          currentStepDefinition: mockGaStep2,
          status: WorkflowStatus.IN_PROGRESS,
          currentStepTransitionedAt: mockNow,
          currentStepEndsAt: null,
        }),
      );
      expect(result).toEqual({
        stepDefinitionCode: mockGaStep2.code,
        status: WorkflowStatus.IN_PROGRESS,
      });
    });

    it('should throw UnprocessableEntityException if next step definition is not found', async () => {
      const expectedError = new UnprocessableEntityException('Next step not found');
      const getNextStepSpy = jest.spyOn(service as any, 'getNextStepDefinition').mockRejectedValueOnce(expectedError);
      const transitionPromise = service.transitionSingleStateToNextStep(WorkflowEntityType.CALL, entityId);

      await expect(transitionPromise).rejects.toEqual(expectedError);

      expect(service.findWorkflowStateByEntity).toHaveBeenCalledWith(WorkflowEntityType.CALL, entityId, undefined);
      expect(getNextStepSpy).toHaveBeenCalled();
      expect(entityManager.save).not.toHaveBeenCalled();
    });

    it('should throw UnprocessableEntityException when transitioning from a terminal step', async () => {
      const terminalState = { ...mockCallState, currentStepDefinition: mockGcStepTerminal };
      const expectedError = new UnprocessableEntityException(
        `Cannot transition from terminal step '${mockGcStepTerminal.name}'.`,
      );

      const findStateSpy = jest.spyOn(service, 'findWorkflowStateByEntity').mockResolvedValue(terminalState);

      jest.spyOn(service as any, 'getNextStepDefinition').mockRestore();

      const transitionPromise = service.transitionSingleStateToNextStep(WorkflowEntityType.CALL, entityId);

      await expect(transitionPromise).rejects.toEqual(expectedError);

      expect(findStateSpy).toHaveBeenCalledWith(WorkflowEntityType.CALL, entityId, undefined);
      expect(entityManager.save).not.toHaveBeenCalled();
    });
  });

  describe('bulkTransitionReadyApplicationsForCall', () => {
    const grantCallId = 1;
    const currentStepDefId = mockGaStep1.id;
    const nextStepDef = mockGaStep2;
    const eligibleStateIds = [201, 202, 203];
    const mockNow = new Date('2023-10-28T11:00:00Z');
    const mockRelevantReadyStatesData = [
      {
        id: 201,
        status: WorkflowStatus.READY_FOR_NEXT_STEP,
        currentStepDefinitionId: currentStepDefId,
      },
      { id: 202, status: WorkflowStatus.READY_FOR_NEXT_STEP, currentStepDefinitionId: currentStepDefId },
      { id: 203, status: WorkflowStatus.READY_FOR_NEXT_STEP, currentStepDefinitionId: currentStepDefId },
    ];

    let mockTxStatesQueryBuilder;
    let mockTxUpdateQueryBuilder;

    beforeEach(() => {
      jest.useFakeTimers().setSystemTime(mockNow);

      (stepDefinitionRepository.findOneBy as any).mockResolvedValue(mockGaStep1);
      jest.spyOn(service as any, 'getNextStepDefinition').mockResolvedValue(nextStepDef);

      mockTxStatesQueryBuilder = createMockQueryBuilder();
      mockTxUpdateQueryBuilder = createMockQueryBuilder();

      dataSource.manager.transaction = jest.fn().mockImplementation(async (cb) => {
        const mockTxManager = createMockEntityManager();
        mockTxManager.createQueryBuilder = jest.fn().mockReturnValue(mockTxStatesQueryBuilder);
        mockTxManager.getRepository = jest.fn().mockImplementation((entity) => {
          if (entity === WorkflowState) {
            const repo = createMockRepository();
            repo.createQueryBuilder = jest.fn().mockReturnValue(mockTxUpdateQueryBuilder);
            return repo;
          }
          return createMockRepository();
        });

        mockTxStatesQueryBuilder.getMany.mockResolvedValue(mockRelevantReadyStatesData);
        mockTxUpdateQueryBuilder.execute.mockResolvedValue({ affected: eligibleStateIds.length } as UpdateResult);

        return cb(mockTxManager);
      });

      (stepDefinitionRepository.findOneBy as any).mockResolvedValue(mockGaStep1);
      jest.spyOn(service as any, 'getNextStepDefinition').mockResolvedValue(nextStepDef);
    });

    it('should transition eligible applications and return the count', async () => {
      const result = await service.bulkTransitionReadyApplicationsForCall(grantCallId, currentStepDefId);

      expect(stepDefinitionRepository.findOneBy).toHaveBeenCalledWith({ id: currentStepDefId });
      expect((service as any).getNextStepDefinition).toHaveBeenCalledWith(mockGaStep1);
      expect(dataSource.manager.transaction).toHaveBeenCalled();

      expect(mockTxStatesQueryBuilder.innerJoin).toHaveBeenCalledWith(
        'grant_application',
        'ga',
        'ga.workflowStateId = ws.id',
      );
      expect(mockTxStatesQueryBuilder.where).toHaveBeenCalledWith('ga.grantCallId = :grantCallId', { grantCallId });
      expect(mockTxStatesQueryBuilder.andWhere).toHaveBeenCalledWith('ws.currentStepDefinitionId = :currentStepDefId', {
        currentStepDefId,
      });
      expect(mockTxStatesQueryBuilder.andWhere).toHaveBeenCalledWith('ws.status NOT IN (:...excludedStatuses)', {
        excludedStatuses: [WorkflowStatus.REJECTED, WorkflowStatus.WITHDRAWN],
      });
      expect(mockTxStatesQueryBuilder.select).toHaveBeenCalledWith(['ws.id', 'ws.status']);
      expect(mockTxStatesQueryBuilder.getMany).toHaveBeenCalled();

      expect(mockTxUpdateQueryBuilder.update).toHaveBeenCalledWith(WorkflowState);

      expect(mockTxUpdateQueryBuilder.set).toHaveBeenCalledWith({ currentStepEndsAt: mockNow });
      expect(mockTxUpdateQueryBuilder.whereInIds).toHaveBeenCalledWith(eligibleStateIds);
      expect(mockTxUpdateQueryBuilder.andWhere).toHaveBeenCalledWith('currentStepEndsAt IS NULL');

      expect(mockTxUpdateQueryBuilder.set).toHaveBeenCalledWith({
        currentStepDefinitionId: nextStepDef.id,
        status: WorkflowStatus.IN_PROGRESS,
        currentStepTransitionedAt: mockNow,
        currentStepEndsAt: null,
      });
      expect(mockTxUpdateQueryBuilder.whereInIds).toHaveBeenCalledWith(eligibleStateIds);

      expect(mockTxUpdateQueryBuilder.execute).toHaveBeenCalledTimes(2);

      expect(result).toEqual({ transitionedCount: eligibleStateIds.length });
    });

    it('should return 0 transitioned count if no applications are ready', async () => {
      (stepDefinitionRepository.findOneBy as any).mockResolvedValue(mockGaStep1);
      jest.spyOn(service as any, 'getNextStepDefinition').mockResolvedValue(nextStepDef);

      const testTxStatesQueryBuilder = createMockQueryBuilder();
      const testTxUpdateQueryBuilder = createMockQueryBuilder();

      testTxStatesQueryBuilder.getMany.mockResolvedValue([]);

      dataSource.manager.transaction = jest.fn().mockImplementation(async (cb) => {
        const mockTxManager = createMockEntityManager();
        mockTxManager.createQueryBuilder = jest.fn().mockReturnValue(testTxStatesQueryBuilder);
        mockTxManager.getRepository = jest.fn().mockImplementation((entity) => {
          if (entity === WorkflowState) {
            const repo = createMockRepository();
            repo.createQueryBuilder = jest.fn().mockReturnValue(testTxUpdateQueryBuilder);
            return repo;
          }
          return createMockRepository();
        });
        return cb(mockTxManager);
      });

      const result = await service.bulkTransitionReadyApplicationsForCall(grantCallId, currentStepDefId);

      expect(stepDefinitionRepository.findOneBy).toHaveBeenCalledWith({ id: currentStepDefId });
      expect((service as any).getNextStepDefinition).toHaveBeenCalledWith(mockGaStep1);
      expect(dataSource.manager.transaction).toHaveBeenCalled();

      expect(testTxStatesQueryBuilder.innerJoin).toHaveBeenCalled();
      expect(testTxStatesQueryBuilder.where).toHaveBeenCalledWith('ga.grantCallId = :grantCallId', { grantCallId });
      expect(testTxStatesQueryBuilder.andWhere).toHaveBeenCalledWith('ws.currentStepDefinitionId = :currentStepDefId', {
        currentStepDefId,
      });
      expect(testTxStatesQueryBuilder.andWhere).toHaveBeenCalledWith('ws.status NOT IN (:...excludedStatuses)', {
        excludedStatuses: [WorkflowStatus.REJECTED, WorkflowStatus.WITHDRAWN],
      });
      expect(testTxStatesQueryBuilder.select).toHaveBeenCalledWith(['ws.id', 'ws.status']);
      expect(testTxStatesQueryBuilder.getMany).toHaveBeenCalledTimes(1);

      expect(testTxUpdateQueryBuilder.update).not.toHaveBeenCalled();
      expect(testTxUpdateQueryBuilder.set).not.toHaveBeenCalled();
      expect(testTxUpdateQueryBuilder.whereInIds).not.toHaveBeenCalled();
      expect(testTxUpdateQueryBuilder.execute).not.toHaveBeenCalled();

      expect(result).toEqual({ transitionedCount: 0 });
    });

    it('should throw UnprocessableEntityException if next step definition is not found (within transaction)', async () => {
      (stepDefinitionRepository.findOneBy as any).mockResolvedValue(mockGaStep1);

      const expectedError = new UnprocessableEntityException('Next step not found');
      const getNextStepSpy = jest.spyOn(service as any, 'getNextStepDefinition').mockRejectedValueOnce(expectedError);
      const mockTxManager = createMockEntityManager();
      const bulkPromise = service.bulkTransitionReadyApplicationsForCall(grantCallId, currentStepDefId);

      await expect(bulkPromise).rejects.toEqual(expectedError);

      expect(stepDefinitionRepository.findOneBy).toHaveBeenCalledWith({ id: currentStepDefId });
      expect(dataSource.manager.transaction).toHaveBeenCalledTimes(1);
      expect(getNextStepSpy).toHaveBeenCalledWith(mockGaStep1);

      expect(mockTxManager.createQueryBuilder).not.toHaveBeenCalled();
      expect(mockTxManager.getRepository).not.toHaveBeenCalled();
    });
  });

  describe('updateStateStatus', () => {
    const applicationId = 1;
    const stateId = 205;

    beforeEach(() => {
      const defaultAppState = {
        ...mockAppState,
        id: stateId,
        status: WorkflowStatus.IN_PROGRESS,
        currentStepDefinitionId: mockGaStep1.id,
        currentStepDefinition: mockGaStep1,
      };

      jest.spyOn(service, 'findWorkflowStateByEntity').mockResolvedValue(defaultAppState);
      entityManager.save = jest.fn().mockImplementation(async (_, state) => state);
    });

    it('should update status successfully', async () => {
      const newState = await service.updateStateStatus(
        WorkflowEntityType.APPLICATION,
        applicationId,
        WorkflowStatus.READY_FOR_NEXT_STEP
      );

      expect(service.findWorkflowStateByEntity).toHaveBeenCalledWith(
        WorkflowEntityType.APPLICATION,
        applicationId,
        dataSource.manager,
      );

      expect(entityManager.save).toHaveBeenCalledWith(
        WorkflowState,
        expect.objectContaining({
          id: stateId,
          status: WorkflowStatus.READY_FOR_NEXT_STEP,
        }),
      );

      expect(newState.status).toBe(WorkflowStatus.READY_FOR_NEXT_STEP);
    });

    it.each([WorkflowStatus.REJECTED, WorkflowStatus.WITHDRAWN])(
      'should throw UnprocessableEntityException trying to change from terminal status %s',
      async (terminalStatus) => {
        const terminalState = {
          ...mockAppState,
          id: stateId,
          status: terminalStatus,
          currentStepDefinition: mockGaStep1,
        };

        const findStateSpy = jest.spyOn(service, 'findWorkflowStateByEntity').mockResolvedValueOnce(terminalState);

        const expectedErrorMessage = `Cannot change workflow status from existing terminal status '${terminalStatus}'.`;
        const expectedError = new UnprocessableEntityException(expectedErrorMessage);
        const updatePromise = service.updateStateStatus(
          WorkflowEntityType.APPLICATION,
          applicationId,
          WorkflowStatus.IN_PROGRESS
        );

        await expect(updatePromise).rejects.toEqual(expectedError);

        expect(findStateSpy).toHaveBeenCalledWith(WorkflowEntityType.APPLICATION, applicationId, dataSource.manager);

        expect(entityManager.save).not.toHaveBeenCalled();
      },
    );

    it('should throw UnprocessableEntityException trying to set the same status', async () => {
      await expect(
        service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.IN_PROGRESS)
      ).rejects.toThrow(UnprocessableEntityException);

      await expect(
        service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.IN_PROGRESS)
      ).rejects.toThrow("Workflow status is already 'IN_PROGRESS'.");

      expect(entityManager.save).not.toHaveBeenCalled();
    });

    it('should throw UnprocessableEntityException trying to set APPROVED at wrong stage', async () => {
      await expect(
        service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.APPROVED)
      ).rejects.toThrow(UnprocessableEntityException);

      await expect(
        service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.APPROVED)
      ).rejects.toThrow(
        `Applications can only have status set to APPROVED when in stage '${StageCode.GA_FINAL_QUALIFICATION}'. Current: ${StageCode.GA_SCREENING}`,
      );

      expect(entityManager.save).not.toHaveBeenCalled();
    });

    it('should successfully set APPROVED status at the correct stage (GA_FINAL_QUALIFICATION)', async () => {
      const correctStageState = {
        ...mockAppState,
        id: stateId,
        status: WorkflowStatus.IN_PROGRESS,
        currentStepDefinitionId: mockGaStepFinalQual.id,
        currentStepDefinition: mockGaStepFinalQual,
      };

      jest.spyOn(service, 'findWorkflowStateByEntity').mockResolvedValueOnce(correctStageState);

      const newState = await service.updateStateStatus(
        WorkflowEntityType.APPLICATION,
        applicationId,
        WorkflowStatus.APPROVED
      );

      expect(entityManager.save).toHaveBeenCalledWith(
        WorkflowState,
        expect.objectContaining({
          id: stateId,
          status: WorkflowStatus.APPROVED,
        }),
      );

      expect(newState.status).toBe(WorkflowStatus.APPROVED);
    });

    it('should throw UnprocessableEntityException trying to change status from CLOSED terminal status', async () => {
      const closedState = {
        ...mockAppState,
        id: stateId,
        status: WorkflowStatus.CLOSED,
      };

      jest.spyOn(service, 'findWorkflowStateByEntity').mockResolvedValueOnce(closedState);

      await expect(
        service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.IN_PROGRESS)
      ).rejects.toThrow(UnprocessableEntityException);

      await expect(
        service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.IN_PROGRESS)
      ).rejects.toThrow(`Cannot change workflow status from existing terminal status '${WorkflowStatus.CLOSED}'.`);

      expect(entityManager.save).not.toHaveBeenCalled();
    });

    it('should successfully set CLOSED status', async () => {
      const newState = await service.updateStateStatus(
        WorkflowEntityType.APPLICATION,
        applicationId,
        WorkflowStatus.CLOSED
      );

      expect(service.findWorkflowStateByEntity).toHaveBeenCalledWith(
        WorkflowEntityType.APPLICATION,
        applicationId,
        dataSource.manager,
      );

      expect(entityManager.save).toHaveBeenCalledWith(
        WorkflowState,
        expect.objectContaining({
          id: stateId,
          status: WorkflowStatus.CLOSED,
        }),
      );

      expect(newState.status).toBe(WorkflowStatus.CLOSED);
    });

    it('should throw UnprocessableEntityException when trying to change from CLOSED to any other status', async () => {
      const closedState = {
        ...mockAppState,
        id: stateId,
        status: WorkflowStatus.CLOSED,
      };

      jest.spyOn(service, 'findWorkflowStateByEntity').mockResolvedValueOnce(closedState);

      await expect(
        service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.APPROVED)
      ).rejects.toThrow(UnprocessableEntityException);

      await expect(
        service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.APPROVED)
      ).rejects.toThrow(`Cannot change workflow status from existing terminal status '${WorkflowStatus.CLOSED}'.`);

      expect(entityManager.save).not.toHaveBeenCalled();
    });

    it('should throw UnprocessableEntityException when trying to change from CLOSED to IN_PROGRESS', async () => {
      const closedState = {
        ...mockAppState,
        id: stateId,
        status: WorkflowStatus.CLOSED,
      };

      jest.spyOn(service, 'findWorkflowStateByEntity').mockResolvedValueOnce(closedState);

      await expect(
        service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.IN_PROGRESS)
      ).rejects.toThrow(UnprocessableEntityException);

      await expect(
        service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.IN_PROGRESS)
      ).rejects.toThrow(`Cannot change workflow status from existing terminal status '${WorkflowStatus.CLOSED}'.`);

      expect(entityManager.save).not.toHaveBeenCalled();
    });

    it('should throw UnprocessableEntityException when trying to set CLOSED status twice', async () => {
      const closedState = {
        ...mockAppState,
        id: stateId,
        status: WorkflowStatus.CLOSED,
      };

      jest.spyOn(service, 'findWorkflowStateByEntity').mockResolvedValueOnce(closedState);

      await expect(
        service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.CLOSED)
      ).rejects.toThrow(UnprocessableEntityException);

      await expect(
        service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.CLOSED)
      ).rejects.toThrow(`Cannot change workflow status from existing terminal status '${WorkflowStatus.CLOSED}'.`);

      expect(entityManager.save).not.toHaveBeenCalled();
    });

    it('should validate all terminal statuses are properly handled', async () => {
      const terminalStatuses = [WorkflowStatus.REJECTED, WorkflowStatus.WITHDRAWN, WorkflowStatus.CLOSED];

      for (const terminalStatus of terminalStatuses) {
        const terminalState = {
          ...mockAppState,
          id: stateId,
          status: terminalStatus,
        };

        jest.spyOn(service, 'findWorkflowStateByEntity').mockResolvedValueOnce(terminalState);

        await expect(
          service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.IN_PROGRESS)
        ).rejects.toThrow(UnprocessableEntityException);

        await expect(
          service.updateStateStatus(WorkflowEntityType.APPLICATION, applicationId, WorkflowStatus.IN_PROGRESS)
        ).rejects.toThrow(`Cannot change workflow status from existing terminal status '${terminalStatus}'.`);
      }

      expect(entityManager.save).not.toHaveBeenCalled();
    });
  });
});
