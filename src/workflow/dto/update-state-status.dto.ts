import { <PERSON><PERSON><PERSON>, IsNotEmpty, <PERSON><PERSON>otIn, IsString, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { WorkflowStatus } from '../enums/workflow-status.enum';

export class UpdateStateStatusDto {
  @ApiProperty({ enum: WorkflowStatus, enumName: 'WorkflowStatus' })
  @IsNotEmpty()
  @IsEnum(WorkflowStatus)
  @IsNotIn([WorkflowStatus.WITHDRAWN, WorkflowStatus.CLOSED], {
    message: `Coordinator cannot change status to '${WorkflowStatus.WITHDRAWN}' or '${WorkflowStatus.CLOSED}'. Use dedicated endpoints for these actions.`,
  })
  status: WorkflowStatus;

  @ApiProperty({ description: 'Reason for the action', type: String })
  @IsNotEmpty()
  @IsString()
  @MaxLength(1000)
  reason: string;
}
