import { HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { of, throwError } from 'rxjs';

import { BalanceAlertService } from './balance-alert.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { MailService } from '../notifications/mail/mail.service';
import { Repository } from 'typeorm';
import { User } from '../auth/entities/user.entity';
import { getRepositoryToken } from '@nestjs/typeorm';

describe('BalanceAlertService', () => {
  let service: BalanceAlertService;
  let httpService: HttpService;
  let mailService: MailService;
  let userRepository: Repository<User>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BalanceAlertService,
        ConfigService,
        {
          provide: MailService,
          useValue: {
            sendBalanceAlertEmail: jest.fn(),
          },
        },
        {
          provide: HttpService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get(BalanceAlertService);
    httpService = module.get(HttpService);
    mailService = module.get(MailService);
    userRepository = module.get(getRepositoryToken(User));
  });

  describe('checkBalance', () => {
    it('should throw failed dependency error when the request to the mirror node fails', async () => {
      const walletAddress = '0.0.**********';
      jest.spyOn(userRepository, 'findOne').mockResolvedValue({
        email: '<EMAIL>',
        displayName: 'Test User',
      } as User);

      jest.spyOn(httpService, 'get').mockReturnValue(throwError(() => new Error('Failed to fetch balance')));

      await expect(service.checkBalance(walletAddress)).rejects.toThrow(
        new HttpException('Request to hedera mirror failed', HttpStatus.FAILED_DEPENDENCY),
      );
    });

    it('should throw not found error when the user is not found', async () => {
      const walletAddress = '0.0.**********';

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      await expect(service.checkBalance(walletAddress)).rejects.toThrow(NotFoundException);
    });

    it('should send a critical email when the balance is below 25 Hbar', async () => {
      const walletAddress = '0.0.**********';

      jest.spyOn(httpService, 'get').mockReturnValue(
        of({
          data: {
            balance: {
              // 24 Hbar in tinybars
              balance: 2400000000,
            },
          },
        } as any),
      );

      jest.spyOn(userRepository, 'findOne').mockResolvedValue({
        email: '<EMAIL>',
        displayName: 'Test User',
      } as User);

      await service.checkBalance(walletAddress);

      expect(mailService.sendBalanceAlertEmail).toHaveBeenCalledWith('<EMAIL>', 'Test User', 'critical', 24);
    });

    it('should send an alert email when the balance is below 50 Hbar', async () => {
      const walletAddress = '0.0.**********';

      jest.spyOn(httpService, 'get').mockReturnValue(
        of({
          data: {
            balance: {
              balance: 4900000000,
            },
          },
        } as any),
      );

      jest.spyOn(userRepository, 'findOne').mockResolvedValue({
        email: '<EMAIL>',
        displayName: 'Test User',
      } as User);

      await service.checkBalance(walletAddress);

      expect(mailService.sendBalanceAlertEmail).toHaveBeenCalledWith('<EMAIL>', 'Test User', 'alert', 49);
    });

    it('should send a warning email when the balance is below 100 Hbar', async () => {
      const walletAddress = '0.0.**********';

      jest.spyOn(httpService, 'get').mockReturnValue(
        of({
          data: {
            balance: {
              balance: 9900000000,
            },
          },
        } as any),
      );

      jest.spyOn(userRepository, 'findOne').mockResolvedValue({
        email: '<EMAIL>',
        displayName: 'Test User',
      } as User);

      await service.checkBalance(walletAddress);

      expect(mailService.sendBalanceAlertEmail).toHaveBeenCalledWith('<EMAIL>', 'Test User', 'warning', 99);
    });

    it('should not send an email when the balance is above 100 Hbar', async () => {
      const walletAddress = '0.0.**********';

      jest.spyOn(httpService, 'get').mockReturnValue(
        of({
          data: {
            balance: {
              balance: 10100000000,
            },
          },
        } as any),
      );

      jest.spyOn(userRepository, 'findOne').mockResolvedValue({
        email: '<EMAIL>',
        displayName: 'Test User',
      } as User);

      await service.checkBalance(walletAddress);

      expect(mailService.sendBalanceAlertEmail).not.toHaveBeenCalled();
    });
  });
});
