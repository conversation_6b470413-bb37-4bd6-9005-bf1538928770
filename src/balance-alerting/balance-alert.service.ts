import { <PERSON><PERSON>, <PERSON>barUnit } from '@hashgraph/sdk';
import { HttpException, HttpStatus, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { catchError, firstValueFrom, map, throwError } from 'rxjs';

import { AccountDetailsResponse } from './types/account-details-response.type';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { MailService } from '../notifications/mail/mail.service';
import { User } from '../auth/entities/user.entity';
import { ArrayContains, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class BalanceAlertService {
  private readonly logger = new Logger(BalanceAlertService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly mailService: MailService,
    // TODO: Remove this once we have a proper user service
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async checkBalance(walletAddress: string) {
    const user = await this.userRepository.findOne({
      where: {
        addresses: ArrayContains([walletAddress]),
      },
    });

    if (!user) {
      this.logger.error(`User not found for wallet address: ${walletAddress}`);
      throw new NotFoundException('User not found');
    }

    const balanceUrl = `${this.configService.get('MIRROR_NODE_URL')}/api/v1/accounts/${walletAddress}`;
    const tinybarsBalance = await firstValueFrom(
      this.httpService.get<AccountDetailsResponse>(balanceUrl).pipe(
        map((response) => response.data.balance.balance),
        catchError((err) => {
          this.logger.error(err.message);
          return throwError(() => new HttpException('Request to hedera mirror failed', HttpStatus.FAILED_DEPENDENCY));
        }),
      ),
    );

    const hbarBalance = Hbar.fromTinybars(tinybarsBalance).to(HbarUnit.Hbar);

    const thresholds = [
      { limit: 25, level: 'critical' },
      { limit: 50, level: 'alert' },
      { limit: 100, level: 'warning' },
    ] as const;

    const threshold = thresholds.find((t) => hbarBalance.toNumber() <= t.limit);

    if (!threshold) {
      // Balance is above 100 Hbar
      return;
    }

    const alertLevel = threshold.level;

    this.mailService.sendBalanceAlertEmail(user.email, user.displayName, alertLevel, hbarBalance.toNumber());
  }
}
