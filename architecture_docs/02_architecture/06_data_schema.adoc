== Data schema

The following diagram displays the current data schema used for the application.

[plantuml]
----
@startuml

enum grant_application_status_enum {
  OPEN
  WITHDRAWN
  REJECTED
  APPROVED
}

enum grant_program_status_enum {
  OPEN
  CLOSED
}

enum user_role_enum {
  GRANT_PROGRAM_COORDINATOR
  USER
}

class grant_application {
  +id : integer
  +title : varchar
  +description : varchar
  +companyName : varchar
  +companyCountry : varchar
  +companyWebpage : varchar
  +industry : varchar
  +contactFullName : varchar
  +contactEmail : varchar
  +contactPhoneNumber : varchar
  +createdAt : timestamp
  +status : grant_application_status_enum
  +statusChangedOn : timestamp
  +firstVotingTopicId: varchar
  +secondVotingTopicId: varchar
  +grantCallId : integer
  +assigneeId : integer
  +firstCommunityVoteId : integer
  +secondCommunityVoteId : integer
}

class grant_application_votes {
  +id : integer
  +inFavorVotes: integer
  +againstVotes: integer
  +walletsInFavor : string[]
  +walletsAgainst : string[]
  +createdAt : timestamp
  +updatedAt : timestamp
}

class grant_application_member {
  +id : integer
  +isCreator : boolean
  +userId : integer
  +grantApplicationId : integer
}

class grant_application_stage {
  +id : integer
  +title : varchar
  +subtitle : varchar
  +description : varchar
  +expectedDuration : integer
  +position : integer
  +actionDescription : varchar
  +actionButtonText : varchar
  +actionLink : varchar
  +grantCallId : integer
}

class grant_application_stage_log {
  +id : integer
  +startedOn : timestamp
  +finishedOn : timestamp
  +grantApplicationId : integer
  +stageId : integer
}

class grant_call {
  +id : integer
  +grantCallSlug : varchar
  +name : varchar
  +description : varchar
  +businessCategory : varchar
  +targetedIndustries : text
  +startDate : timestamp
  +endDate : timestamp
  +isClosed : boolean
  +minGrantSize : numeric(12,0)
  +maxGrantSize : numeric(12,0)
  +createdAt : timestamp
  +updatedAt : timestamp
  +grantProgramId : integer
}

class grant_call_member {
  +id : integer
  +isCoordinator : boolean
  +userId : integer
  +grantCallId : integer
}

class grant_program {
  +id : integer
  +grantProgramSlug : varchar
  +name : varchar
  +description : varchar
  +scope : varchar
  +budget : numeric(12,0)
  +grantorPublicProfileName : varchar
  +grantorLogoURL : varchar
  +grantorPublicProfileSlug : varchar
  +status : grant_program_status_enum
  +createdAt : timestamp
  +updatedAt : timestamp
  +grantProgramCoordinatorId : integer
  +grantorDescription : varchar
}

class user {
  +id : integer
  +email : varchar (unique)
  +addresses : varchar (not nullable)
  +displayName : varchar
  +emailVerified : boolean (default: false)
  +phoneNumber : varchar (unique, nullable)
  +isPhoneVerified : boolean (default: false)
  +phoneOtp : bigint (nullable)
  +phoneOtpExpiresAt : date (nullable)
  +otp : bigint (nullable)
  +otpExpiresAt : date (nullable)
  +createdAt : date (auto-generated)
  +role : enum (default: 'USER')
  +deletedAt : date (nullable, soft delete)
  +reactivatedAt : date (nullable)
  +linkedAccounts : jsonb (nullable)
}

class user_notification_preferences {
    +id: number (Primary Key)
    +userId: number (Foreign Key)
    +notificationType: string
    +enabled: boolean (default: true)
    +createdAt: Date (timestamp, auto-gen)
    +updatedAt: Date (timestamp, auto-gen)
}

grant_application "1" -- "*" grant_application_stage_log : logs
user "1" -- "*" grant_program : manages
grant_application_stage "1" -- "*" grant_application_stage_log : stages
grant_program "1" -- "*" grant_call : programs
user "1" -- "*" grant_application_member : participates
user "1" -- "*" grant_call_member : coordinates
grant_call "1" -- "*" grant_call_member : calls
grant_call "1" -- "*" grant_application : applications
grant_call_member "1" -- "*" grant_application : assigns
grant_application "1" -- "*" grant_application_member : members
grant_call "1" -- "*" grant_application_stage : stages
user "1" -- "1" user_role_enum : role
grant_program "1" -- "1" grant_program_status_enum : status
grant_application "1" -- "1" grant_application_status_enum : status
user "1" -- "*" user_notification_preferences
grant_application "1" -- "1" grant_application_votes

@enduml
----
